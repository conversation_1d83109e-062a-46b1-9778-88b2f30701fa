package com.example.watermark_camera

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.util.Log

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.example.watermark_camera/camera_optimization"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "optimizeCameraBuffers" -> {
                    try {
                        // 优化相机缓冲区设置
                        optimizeCameraBuffers()
                        result.success("Camera buffers optimized")
                    } catch (e: Exception) {
                        Log.e("CameraOptimization", "Failed to optimize camera buffers", e)
                        result.error("OPTIMIZATION_FAILED", e.message, null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun optimizeCameraBuffers() {
        // 设置系统属性来优化相机缓冲区
        try {
            // 减少ImageReader的最大图像数量
            System.setProperty("camera.imagereader.maxImages", "2")

            // 优化内存分配
            System.setProperty("camera.preview.bufferCount", "3")

            // 强制垃圾回收
            System.gc()

            Log.i("CameraOptimization", "Camera buffer optimization applied")
        } catch (e: Exception) {
            Log.e("CameraOptimization", "Failed to set camera properties", e)
        }
    }
}
