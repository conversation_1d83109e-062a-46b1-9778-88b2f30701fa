import 'package:camera/camera.dart';
import 'package:flutter/material.dart';

/// 镜头类型枚举
enum LensType {
  back,        // 后置主摄
  front,       // 前置
  ultraWide,   // 超广角
  telephoto,   // 长焦
  macro,       // 微距
}

/// 镜头信息类
class LensInfo {
  final CameraDescription camera;
  final LensType type;
  final String displayName;
  final IconData icon;
  final String description;

  LensInfo({
    required this.camera,
    required this.type,
    required this.displayName,
    required this.icon,
    required this.description,
  });
}

/// 相机镜头管理器
class CameraLensManager {
  static final CameraLensManager _instance = CameraLensManager._internal();
  factory CameraLensManager() => _instance;
  CameraLensManager._internal();

  List<LensInfo> _availableLenses = [];
  int _currentLensIndex = 0;

  List<LensInfo> get availableLenses => _availableLenses;
  LensInfo? get currentLens => _availableLenses.isNotEmpty ? _availableLenses[_currentLensIndex] : null;
  int get currentLensIndex => _currentLensIndex;

  /// 初始化可用镜头
  Future<void> initializeLenses() async {
    try {
      print('🔍 开始检测可用镜头...');
      
      final cameras = await availableCameras();
      _availableLenses.clear();

      for (int i = 0; i < cameras.length; i++) {
        final camera = cameras[i];
        final lensInfo = _analyzeLens(camera, i);
        if (lensInfo != null) {
          _availableLenses.add(lensInfo);
          print('📷 发现镜头: ${lensInfo.displayName} (${camera.name})');
        }
      }

      // 确保有默认选择的镜头（优先选择后置主摄）
      _currentLensIndex = _findDefaultLensIndex();
      
      print('✅ 镜头检测完成，共发现 ${_availableLenses.length} 个镜头');
      print('📷 默认选择: ${currentLens?.displayName}');
      
    } catch (e) {
      print('❌ 镜头检测失败: $e');
    }
  }

  /// 分析镜头类型
  LensInfo? _analyzeLens(CameraDescription camera, int index) {
    final name = camera.name.toLowerCase();
    final direction = camera.lensDirection;

    // 前置摄像头
    if (direction == CameraLensDirection.front) {
      return LensInfo(
        camera: camera,
        type: LensType.front,
        displayName: '前置',
        icon: Icons.camera_front,
        description: '自拍摄像头',
      );
    }

    // 后置摄像头分类
    if (direction == CameraLensDirection.back) {
      // 超广角镜头检测
      if (name.contains('ultra') || name.contains('wide') || name.contains('0.5')) {
        return LensInfo(
          camera: camera,
          type: LensType.ultraWide,
          displayName: '超广角',
          icon: Icons.panorama_wide_angle,
          description: '0.5x 超广角镜头',
        );
      }
      
      // 长焦镜头检测
      if (name.contains('tele') || name.contains('zoom') || name.contains('2x') || name.contains('3x')) {
        return LensInfo(
          camera: camera,
          type: LensType.telephoto,
          displayName: '长焦',
          icon: Icons.zoom_in,
          description: '2x-3x 长焦镜头',
        );
      }
      
      // 微距镜头检测
      if (name.contains('macro') || name.contains('close')) {
        return LensInfo(
          camera: camera,
          type: LensType.macro,
          displayName: '微距',
          icon: Icons.center_focus_strong,
          description: '微距摄影镜头',
        );
      }
      
      // 默认为主摄
      return LensInfo(
        camera: camera,
        type: LensType.back,
        displayName: '主摄',
        icon: Icons.camera_alt,
        description: '后置主摄像头',
      );
    }

    return null;
  }

  /// 查找默认镜头索引（优先选择后置主摄）
  int _findDefaultLensIndex() {
    // 优先选择后置主摄
    for (int i = 0; i < _availableLenses.length; i++) {
      if (_availableLenses[i].type == LensType.back) {
        return i;
      }
    }
    
    // 如果没有后置主摄，选择第一个可用镜头
    return _availableLenses.isNotEmpty ? 0 : -1;
  }

  /// 切换到指定镜头
  bool switchToLens(int index) {
    if (index >= 0 && index < _availableLenses.length) {
      _currentLensIndex = index;
      print('📷 切换到镜头: ${currentLens?.displayName}');
      return true;
    }
    return false;
  }

  /// 切换到指定类型的镜头
  bool switchToLensType(LensType type) {
    for (int i = 0; i < _availableLenses.length; i++) {
      if (_availableLenses[i].type == type) {
        return switchToLens(i);
      }
    }
    return false;
  }

  /// 切换到下一个镜头
  bool switchToNextLens() {
    if (_availableLenses.length <= 1) return false;
    
    int nextIndex = (_currentLensIndex + 1) % _availableLenses.length;
    return switchToLens(nextIndex);
  }

  /// 获取指定类型的镜头
  LensInfo? getLensByType(LensType type) {
    for (final lens in _availableLenses) {
      if (lens.type == type) {
        return lens;
      }
    }
    return null;
  }

  /// 检查是否有指定类型的镜头
  bool hasLensType(LensType type) {
    return getLensByType(type) != null;
  }

  /// 获取当前镜头的相机描述
  CameraDescription? get currentCamera => currentLens?.camera;

  /// 获取镜头类型的显示名称
  static String getLensTypeDisplayName(LensType type) {
    switch (type) {
      case LensType.back:
        return '主摄';
      case LensType.front:
        return '前置';
      case LensType.ultraWide:
        return '超广角';
      case LensType.telephoto:
        return '长焦';
      case LensType.macro:
        return '微距';
    }
  }

  /// 获取镜头类型的图标
  static IconData getLensTypeIcon(LensType type) {
    switch (type) {
      case LensType.back:
        return Icons.camera_alt;
      case LensType.front:
        return Icons.camera_front;
      case LensType.ultraWide:
        return Icons.panorama_wide_angle;
      case LensType.telephoto:
        return Icons.zoom_in;
      case LensType.macro:
        return Icons.center_focus_strong;
    }
  }

  /// 重置到默认镜头
  void resetToDefault() {
    _currentLensIndex = _findDefaultLensIndex();
  }
}
