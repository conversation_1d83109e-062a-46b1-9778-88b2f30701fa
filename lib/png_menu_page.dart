import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'dart:ui' as ui;
import 'png_watermark_manager.dart';

class PngMenuPage extends StatefulWidget {
  final CameraController? cameraController;

  const PngMenuPage({super.key, this.cameraController});

  @override
  State<PngMenuPage> createState() => _PngMenuPageState();
}

class _PngMenuPageState extends State<PngMenuPage> {
  final PngWatermarkManager _watermarkManager = PngWatermarkManager();
  bool _isLoading = true;
  bool _isPreviewMode = false;

  // 加载进度相关状态
  int _watermarkLoadedCount = 0;
  int _watermarkTotalCount = 0;
  String _currentLoadingWatermark = '';

  @override
  void initState() {
    super.initState();
    _loadWatermarks();
    _isPreviewMode = _watermarkManager.isPreviewMode;
  }

  Future<void> _loadWatermarks() async {
    // 设置进度回调
    _watermarkManager.setProgressCallback((loaded, total, current) {
      if (mounted) {
        setState(() {
          _watermarkLoadedCount = loaded;
          _watermarkTotalCount = total;
          _currentLoadingWatermark = current;
        });
      }
    });

    // 只下载缓存所有水印（不进行图像解码和缩放）
    await _watermarkManager.downloadAndCacheAllWatermarks();

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _togglePreviewMode() {
    setState(() {
      _isPreviewMode = !_isPreviewMode;
      _watermarkManager.setPreviewMode(_isPreviewMode);
    });
  }

  void _selectWatermark(PngWatermark? watermark) async {
    if (watermark != null) {
      // 首先进行图像解码（如果还没有解码）
      if (watermark.uiImage == null || watermark.processedImage == null) {
        print('🔄 解码水印图像: ${watermark.name}');
        await _watermarkManager.decodeWatermarkFromCache(watermark);
      }

      // 获取屏幕宽度用于水印缩放
      final screenWidth = MediaQuery.of(context).size.width;

      // 在选择水印时进行缩放
      await _watermarkManager.scaleWatermarkToFitScreen(watermark, screenWidth);
    }

    _watermarkManager.setSelectedWatermark(watermark);
    setState(() {}); // 刷新界面以显示控制面板
  }

  void _updateScale(double scale) {
    _watermarkManager.updateWatermarkScale(scale);
    setState(() {});
  }

  void _updateRotation(double rotation) {
    _watermarkManager.updateWatermarkRotation(rotation);
    setState(() {});
  }

  void _updateFlipHorizontal(bool flip) {
    _watermarkManager.updateWatermarkFlipHorizontal(flip);
    setState(() {});
  }

  void _updateFlipVertical(bool flip) {
    _watermarkManager.updateWatermarkFlipVertical(flip);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text('PNG水印选择', style: TextStyle(color: Colors.white)),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _isPreviewMode ? Icons.visibility_off : Icons.visibility,
              color: Colors.white,
            ),
            onPressed: _togglePreviewMode,
            tooltip: _isPreviewMode ? '关闭预览' : '开启预览',
          ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: Container(
                padding: const EdgeInsets.all(32),
                margin: const EdgeInsets.symmetric(horizontal: 32),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const CircularProgressIndicator(color: Colors.blue),
                    const SizedBox(height: 24),
                    const Text(
                      '正在加载PNG水印',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // 进度条
                    if (_watermarkTotalCount > 0) ...[
                      LinearProgressIndicator(
                        value: _watermarkTotalCount > 0
                            ? _watermarkLoadedCount / _watermarkTotalCount
                            : 0.0,
                        backgroundColor: Colors.grey[700],
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          Colors.blue,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '$_watermarkLoadedCount/$_watermarkTotalCount',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (_currentLoadingWatermark.isNotEmpty)
                        Text(
                          '正在加载: $_currentLoadingWatermark',
                          style: const TextStyle(
                            color: Colors.white60,
                            fontSize: 14,
                          ),
                          textAlign: TextAlign.center,
                        ),
                    ] else ...[
                      const Text(
                        '初始化中...',
                        style: TextStyle(color: Colors.grey, fontSize: 14),
                      ),
                    ],
                  ],
                ),
              ),
            )
          : Column(
              children: [
                // 无水印选项
                _buildWatermarkOption(null, '无水印', Icons.clear),

                // PNG水印列表
                Expanded(
                  flex: 2,
                  child: _isPreviewMode
                      ? _buildPreviewGrid()
                      : ListView.builder(
                          itemCount: _watermarkManager.watermarks.length,
                          itemBuilder: (context, index) {
                            final watermark =
                                _watermarkManager.watermarks[index];
                            return _buildWatermarkOption(
                              watermark,
                              watermark.name,
                              null,
                            );
                          },
                        ),
                ),

                // 缩放和旋转控制面板
                if (_watermarkManager.selectedWatermark != null)
                  _buildControlPanel(),
              ],
            ),
    );
  }

  Widget _buildWatermarkOption(
    PngWatermark? watermark,
    String title,
    IconData? icon,
  ) {
    final isSelected = _watermarkManager.selectedWatermark == watermark;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isSelected
            ? Colors.blue.withOpacity(0.3)
            : Colors.grey.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: isSelected
            ? Border.all(color: Colors.blue, width: 2)
            : Border.all(color: Colors.transparent, width: 2),
      ),
      child: InkWell(
        onTap: () => _selectWatermark(watermark),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          height: 120,
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // 预览区域
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.white24),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Stack(
                    children: [
                      // 相机预览背景（如果开启预览模式）
                      if (_isPreviewMode && widget.cameraController != null)
                        Positioned.fill(
                          child: widget.cameraController!.value.isInitialized
                              ? CameraPreview(widget.cameraController!)
                              : Container(color: Colors.grey[800]),
                        ),

                      // PNG水印叠加
                      if (watermark?.uiImage != null)
                        Positioned.fill(
                          child: CustomPaint(
                            painter: WatermarkPreviewPainter(
                              watermark!.uiImage!,
                            ),
                          ),
                        ),

                      // 无水印图标
                      if (watermark == null && icon != null)
                        Center(
                          child: Icon(icon, color: Colors.white, size: 32),
                        ),
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // 标题和描述
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      watermark == null ? '不使用任何PNG水印' : '点击选择此PNG水印',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.7),
                        fontSize: 14,
                      ),
                    ),
                    if (watermark?.originalSize != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        '尺寸: ${watermark!.originalSize!.width.toInt()}x${watermark.originalSize!.height.toInt()}',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.5),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // 选中指示器
              if (isSelected)
                const Icon(Icons.check_circle, color: Colors.blue, size: 24),
            ],
          ),
        ),
      ),
    );
  }

  // 构建预览模式的网格布局（每行两个，竖向布局，隐藏详细信息）
  Widget _buildPreviewGrid() {
    return Stack(
      children: [
        // 背景相机预览（完整显示）
        if (widget.cameraController != null &&
            widget.cameraController!.value.isInitialized)
          Positioned.fill(child: CameraPreview(widget.cameraController!)),

        // 轻微半透明遮罩
        Positioned.fill(child: Container(color: Colors.black.withOpacity(0.2))),

        // 水印选项网格（每行两个，竖向布局）
        Positioned.fill(
          child: GridView.builder(
            padding: const EdgeInsets.all(16),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2, // 每行两个
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 0.8, // 竖向比例
            ),
            itemCount:
                _watermarkManager.watermarks.length + 1, // +1 for "无水印"选项
            itemBuilder: (context, index) {
              if (index == 0) {
                // 第一个选项：无水印
                return _buildPreviewNoWatermarkOption();
              } else {
                final watermark = _watermarkManager.watermarks[index - 1];
                return _buildPreviewWatermarkOption(watermark);
              }
            },
          ),
        ),
      ],
    );
  }

  // 构建预览模式的无水印选项
  Widget _buildPreviewNoWatermarkOption() {
    final isSelected = _watermarkManager.selectedWatermark == null;

    return GestureDetector(
      onTap: () => _selectWatermark(null),
      child: Container(
        decoration: BoxDecoration(
          color: isSelected
              ? Colors.blue.withOpacity(0.8)
              : Colors.black.withOpacity(0.6),
          borderRadius: BorderRadius.circular(12),
          border: isSelected
              ? Border.all(color: Colors.blue, width: 3)
              : Border.all(color: Colors.white.withOpacity(0.3)),
        ),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.clear, color: Colors.white, size: 40),
            SizedBox(height: 8),
            Text(
              '无水印',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // 构建预览模式的水印选项（简化版）
  Widget _buildPreviewWatermarkOption(PngWatermark watermark) {
    final isSelected = _watermarkManager.selectedWatermark == watermark;

    return GestureDetector(
      onTap: () => _selectWatermark(watermark),
      child: Container(
        decoration: BoxDecoration(
          color: isSelected
              ? Colors.blue.withOpacity(0.8)
              : Colors.black.withOpacity(0.6),
          borderRadius: BorderRadius.circular(12),
          border: isSelected
              ? Border.all(color: Colors.blue, width: 3)
              : Border.all(color: Colors.white.withOpacity(0.3)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 水印预览图
            if (watermark.uiImage != null)
              Expanded(
                child: Container(
                  margin: const EdgeInsets.all(8),
                  child: CustomPaint(
                    painter: WatermarkPreviewPainter(watermark.uiImage!),
                  ),
                ),
              ),

            // 水印名称
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Text(
                watermark.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建缩放和旋转控制面板
  Widget _buildControlPanel() {
    final selectedWatermark = _watermarkManager.selectedWatermark!;

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题和应用按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '水印调整 - ${selectedWatermark.name}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, selectedWatermark),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
                child: const Text('应用'),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 缩放控制
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '缩放: ${selectedWatermark.scale.toStringAsFixed(2)}x',
                style: const TextStyle(color: Colors.white, fontSize: 14),
              ),
              Slider(
                value: selectedWatermark.scale,
                min: 0.1,
                max: 2.0,
                divisions: 19,
                activeColor: Colors.blue,
                inactiveColor: Colors.grey,
                onChanged: _updateScale,
              ),
            ],
          ),

          const SizedBox(height: 8),

          // 旋转控制
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '旋转: ${(selectedWatermark.rotation * 180 / 3.14159).toStringAsFixed(0)}°',
                style: const TextStyle(color: Colors.white, fontSize: 14),
              ),
              Slider(
                value: selectedWatermark.rotation,
                min: -3.14159,
                max: 3.14159,
                divisions: 36,
                activeColor: Colors.blue,
                inactiveColor: Colors.grey,
                onChanged: _updateRotation,
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 镜像翻转控制
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '镜像翻转',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  // 水平翻转按钮
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _updateFlipHorizontal(
                        !selectedWatermark.flipHorizontal,
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: selectedWatermark.flipHorizontal
                            ? Colors.blue
                            : Colors.grey[700],
                        foregroundColor: Colors.white,
                      ),
                      icon: const Icon(Icons.flip, size: 18),
                      label: const Text('左右翻转'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // 垂直翻转按钮
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () =>
                          _updateFlipVertical(!selectedWatermark.flipVertical),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: selectedWatermark.flipVertical
                            ? Colors.blue
                            : Colors.grey[700],
                        foregroundColor: Colors.white,
                      ),
                      icon: const Icon(Icons.flip, size: 18),
                      label: const Text('上下翻转'),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // 位置复位按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    _watermarkManager.resetWatermarkPosition();
                    setState(() {});
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  icon: const Icon(Icons.center_focus_strong, size: 20),
                  label: const Text('复位位置'),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // 重置按钮
          Center(
            child: TextButton(
              onPressed: () {
                selectedWatermark.scale = 1.0;
                selectedWatermark.rotation = 0.0;
                setState(() {});
              },
              child: const Text('重置', style: TextStyle(color: Colors.grey)),
            ),
          ),
        ],
      ),
    );
  }
}

class WatermarkPreviewPainter extends CustomPainter {
  final ui.Image image;

  WatermarkPreviewPainter(this.image);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..filterQuality = FilterQuality.high;

    // 计算图片在预览区域中的显示尺寸
    final imageAspectRatio = image.width / image.height;
    final containerAspectRatio = size.width / size.height;

    double drawWidth, drawHeight;
    double offsetX = 0, offsetY = 0;

    if (imageAspectRatio > containerAspectRatio) {
      // 图片更宽，以宽度为准
      drawWidth = size.width;
      drawHeight = drawWidth / imageAspectRatio;
      offsetY = (size.height - drawHeight) / 2;
    } else {
      // 图片更高，以高度为准
      drawHeight = size.height;
      drawWidth = drawHeight * imageAspectRatio;
      offsetX = (size.width - drawWidth) / 2;
    }

    final destRect = Rect.fromLTWH(offsetX, offsetY, drawWidth, drawHeight);
    final srcRect = Rect.fromLTWH(
      0,
      0,
      image.width.toDouble(),
      image.height.toDouble(),
    );

    canvas.drawImageRect(image, srcRect, destRect, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
