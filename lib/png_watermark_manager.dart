import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:crypto/crypto.dart';
import 'dart:convert';

class PngWatermark {
  final String url;
  final String name;
  Uint8List? imageData;
  ui.Image? uiImage;
  img.Image? processedImage;
  Size? originalSize;

  // 新增：缩放、旋转、镜像和位置属性
  double scale;
  double rotation; // 旋转角度（弧度）
  bool flipHorizontal; // 水平镜像翻转
  bool flipVertical; // 垂直镜像翻转
  double offsetX; // 水印X轴偏移（相对于屏幕宽度的比例，-1.0到1.0）
  double offsetY; // 水印Y轴偏移（相对于屏幕高度的比例，-1.0到1.0）

  // 缓存相关属性
  bool isLoaded = false;
  DateTime? lastCacheTime;
  String? cacheFilePath;

  PngWatermark({
    required this.url,
    required this.name,
    this.scale = 1.0,
    this.rotation = 0.0,
    this.flipHorizontal = false,
    this.flipVertical = false,
    this.offsetX = 0.0,
    this.offsetY = 0.0,
  });

  // 获取缓存文件名（基于URL的MD5哈希）
  String get cacheFileName {
    final bytes = utf8.encode(url);
    final digest = md5.convert(bytes);
    return '${digest.toString()}.png';
  }
}

class PngWatermarkManager {
  static final PngWatermarkManager _instance = PngWatermarkManager._internal();
  factory PngWatermarkManager() => _instance;
  PngWatermarkManager._internal();

  final List<PngWatermark> _watermarks = [
    PngWatermark(
      url: 'https://oss-hel.vup.tools/img/123-456-789.png',
      name: '水印1',
    ),
    PngWatermark(
      url: 'https://oss-hel.vup.tools/img/987-654-321.png',
      name: '水印2',
    ),
    PngWatermark(
      url: 'https://oss-hel.vup.tools/img/ROG12月透卡v2.png',
      name: 'ROG12月透卡v2',
    ),
    PngWatermark(
      url: 'https://oss-hel.vup.tools/img/啵奇1月透卡 (1).png',
      name: '啵奇1月透卡(1)',
    ),
    PngWatermark(
      url: 'https://oss-hel.vup.tools/img/啵奇1月透卡.png',
      name: '啵奇1月透卡',
    ),
    PngWatermark(url: 'https://oss-hel.vup.tools/img/啵奇_透卡.png', name: '啵奇透卡'),
    PngWatermark(
      url: 'https://oss-hel.vup.tools/img/月隐空夜透卡 (1).png',
      name: '月隐空夜透卡(1)',
    ),
    PngWatermark(
      url: 'https://oss-hel.vup.tools/img/月隐空夜透卡.png',
      name: '月隐空夜透卡',
    ),
    PngWatermark(
      url: 'https://oss-hel.vup.tools/img/舰长-亚克力透卡.png',
      name: '舰长亚克力透卡',
    ),
    PngWatermark(url: 'https://oss-hel.vup.tools/img/舰长-透卡.png', name: '舰长透卡'),
    PngWatermark(url: 'https://oss-hel.vup.tools/img/透卡A.png', name: '透卡A'),
    PngWatermark(url: 'https://oss-hel.vup.tools/img/透卡.png', name: '透卡'),
    PngWatermark(url: 'https://oss-hel.vup.tools/img/鼠鼠透卡.png', name: '鼠鼠透卡'),
  ];

  PngWatermark? _selectedWatermark;
  bool _isPreviewMode = false;
  Directory? _cacheDirectory;

  // 加载进度相关状态
  bool _isLoadingWatermarks = false;
  int _totalWatermarks = 0;
  int _loadedWatermarks = 0;
  String _currentLoadingWatermark = '';
  Function(int loaded, int total, String current)? _progressCallback;

  List<PngWatermark> get watermarks => _watermarks;
  PngWatermark? get selectedWatermark => _selectedWatermark;
  bool get isPreviewMode => _isPreviewMode;

  // 进度相关getter
  bool get isLoadingWatermarks => _isLoadingWatermarks;
  int get totalWatermarks => _totalWatermarks;
  int get loadedWatermarks => _loadedWatermarks;
  String get currentLoadingWatermark => _currentLoadingWatermark;

  void setSelectedWatermark(PngWatermark? watermark) {
    _selectedWatermark = watermark;
  }

  void setPreviewMode(bool enabled) {
    _isPreviewMode = enabled;
  }

  // 设置进度回调
  void setProgressCallback(
    Function(int loaded, int total, String current)? callback,
  ) {
    _progressCallback = callback;
  }

  // 更新选中水印的缩放
  void updateWatermarkScale(double scale) {
    if (_selectedWatermark != null) {
      _selectedWatermark!.scale = scale;
    }
  }

  // 更新选中水印的旋转角度
  void updateWatermarkRotation(double rotation) {
    if (_selectedWatermark != null) {
      _selectedWatermark!.rotation = rotation;
    }
  }

  // 更新选中水印的水平镜像翻转
  void updateWatermarkFlipHorizontal(bool flip) {
    if (_selectedWatermark != null) {
      _selectedWatermark!.flipHorizontal = flip;
    }
  }

  // 更新选中水印的垂直镜像翻转
  void updateWatermarkFlipVertical(bool flip) {
    if (_selectedWatermark != null) {
      _selectedWatermark!.flipVertical = flip;
    }
  }

  // 更新水印位置偏移
  void updateWatermarkOffset(double offsetX, double offsetY) {
    if (_selectedWatermark != null) {
      _selectedWatermark!.offsetX = offsetX.clamp(-1.0, 1.0);
      _selectedWatermark!.offsetY = offsetY.clamp(-1.0, 1.0);
    }
  }

  // 复位水印位置
  void resetWatermarkPosition() {
    if (_selectedWatermark != null) {
      _selectedWatermark!.offsetX = 0.0;
      _selectedWatermark!.offsetY = 0.0;
    }
  }

  // 初始化缓存目录
  Future<void> _initCacheDirectory() async {
    if (_cacheDirectory == null) {
      final appDir = await getApplicationDocumentsDirectory();
      _cacheDirectory = Directory(path.join(appDir.path, 'watermark_cache'));

      if (!await _cacheDirectory!.exists()) {
        await _cacheDirectory!.create(recursive: true);
        print('📁 创建水印缓存目录: ${_cacheDirectory!.path}');
      }
    }
  }

  // 检查缓存是否有效（24小时内）
  bool _isCacheValid(PngWatermark watermark) {
    if (watermark.cacheFilePath == null || watermark.lastCacheTime == null) {
      return false;
    }

    final cacheFile = File(watermark.cacheFilePath!);
    if (!cacheFile.existsSync()) {
      return false;
    }

    final now = DateTime.now();
    final cacheAge = now.difference(watermark.lastCacheTime!);
    return cacheAge.inHours < 24; // 缓存24小时有效
  }

  // 从缓存加载水印（包含完整解码）
  Future<bool> _loadFromCache(PngWatermark watermark) async {
    try {
      await _initCacheDirectory();

      final cacheFile = File(
        path.join(_cacheDirectory!.path, watermark.cacheFileName),
      );
      watermark.cacheFilePath = cacheFile.path;

      if (_isCacheValid(watermark)) {
        print('📦 从缓存加载水印: ${watermark.name}');

        watermark.imageData = await cacheFile.readAsBytes();

        // 解码为ui.Image用于Flutter显示
        final codec = await ui.instantiateImageCodec(watermark.imageData!);
        final frame = await codec.getNextFrame();
        watermark.uiImage = frame.image;
        watermark.originalSize = Size(
          frame.image.width.toDouble(),
          frame.image.height.toDouble(),
        );

        // 解码为img.Image用于图像处理
        watermark.processedImage = img.decodeImage(watermark.imageData!);

        watermark.isLoaded = true;
        print('✅ 缓存水印加载成功: ${watermark.name}');
        return true;
      }
    } catch (e) {
      print('⚠️ 缓存加载失败: $e');
    }
    return false;
  }

  // 从缓存加载水印数据（不解码图像）
  Future<bool> _loadDataFromCache(PngWatermark watermark) async {
    try {
      await _initCacheDirectory();

      final cacheFile = File(
        path.join(_cacheDirectory!.path, watermark.cacheFileName),
      );
      watermark.cacheFilePath = cacheFile.path;

      if (_isCacheValid(watermark)) {
        print('📦 从缓存加载水印数据: ${watermark.name}');
        watermark.imageData = await cacheFile.readAsBytes();
        watermark.isLoaded = true;
        print('✅ 缓存水印数据加载成功: ${watermark.name}');
        return true;
      }
    } catch (e) {
      print('⚠️ 缓存数据加载失败: $e');
    }
    return false;
  }

  // 保存到缓存
  Future<void> _saveToCache(PngWatermark watermark) async {
    try {
      await _initCacheDirectory();

      final cacheFile = File(
        path.join(_cacheDirectory!.path, watermark.cacheFileName),
      );
      await cacheFile.writeAsBytes(watermark.imageData!);

      watermark.cacheFilePath = cacheFile.path;
      watermark.lastCacheTime = DateTime.now();

      print('💾 水印已缓存: ${watermark.name}');
    } catch (e) {
      print('⚠️ 缓存保存失败: $e');
    }
  }

  // 检查缓存文件是否有效（不加载内容）
  Future<bool> _isCacheFileValid(PngWatermark watermark) async {
    try {
      await _initCacheDirectory();

      final cacheFile = File(
        path.join(_cacheDirectory!.path, watermark.cacheFileName),
      );
      watermark.cacheFilePath = cacheFile.path;

      if (_isCacheValid(watermark)) {
        return true;
      }
    } catch (e) {
      print('⚠️ 缓存检查失败: $e');
    }
    return false;
  }

  // 纯下载缓存方法（不进行图像解码）
  Future<bool> downloadAndCacheOnly(PngWatermark watermark) async {
    try {
      // 首先尝试从缓存加载数据（不解码）
      if (await _loadDataFromCache(watermark)) {
        return true;
      }

      print('🌐 下载PNG水印: ${watermark.url}');

      final response = await http.get(Uri.parse(watermark.url));
      if (response.statusCode == 200) {
        watermark.imageData = response.bodyBytes;

        // 只保存到缓存，不进行任何解码处理
        await _saveToCache(watermark);

        watermark.isLoaded = true;
        print('✅ PNG水印下载缓存完成: ${watermark.name}');
        return true;
      } else {
        print('❌ PNG水印下载失败: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('❌ PNG水印下载错误: $e');
      return false;
    }
  }

  // 完整加载水印（包含图像解码）
  Future<bool> loadWatermark(PngWatermark watermark) async {
    try {
      // 首先尝试从缓存加载
      if (await _loadFromCache(watermark)) {
        return true;
      }

      print('🌐 从网络加载PNG水印: ${watermark.url}');

      final response = await http.get(Uri.parse(watermark.url));
      if (response.statusCode == 200) {
        watermark.imageData = response.bodyBytes;

        // 解码为ui.Image用于Flutter显示
        final codec = await ui.instantiateImageCodec(watermark.imageData!);
        final frame = await codec.getNextFrame();

        // 保存原始尺寸和图像，不进行缩放
        double originalWidth = frame.image.width.toDouble();
        double originalHeight = frame.image.height.toDouble();

        watermark.uiImage = frame.image;
        watermark.originalSize = Size(originalWidth, originalHeight);

        // 解码为img.Image用于图像处理
        watermark.processedImage = img.decodeImage(watermark.imageData!);

        // 保存到缓存
        await _saveToCache(watermark);

        watermark.isLoaded = true;
        print('✅ PNG水印加载成功: ${watermark.name}');
        return true;
      } else {
        print('❌ PNG水印加载失败: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('❌ PNG水印加载错误: $e');
      return false;
    }
  }

  // 纯下载缓存所有水印（不进行图像解码）
  Future<void> downloadAndCacheAllWatermarks() async {
    _isLoadingWatermarks = true;
    _totalWatermarks = _watermarks.length;
    _loadedWatermarks = 0;

    print('📥 开始下载缓存所有PNG水印...');

    for (int i = 0; i < _watermarks.length; i++) {
      final watermark = _watermarks[i];
      _currentLoadingWatermark = watermark.name;

      // 调用进度回调
      _progressCallback?.call(
        _loadedWatermarks,
        _totalWatermarks,
        _currentLoadingWatermark,
      );

      await downloadAndCacheOnly(watermark);

      _loadedWatermarks++;

      // 调用进度回调
      _progressCallback?.call(
        _loadedWatermarks,
        _totalWatermarks,
        _currentLoadingWatermark,
      );
    }

    _isLoadingWatermarks = false;
    _currentLoadingWatermark = '';
    print('✅ 所有PNG水印下载缓存完成');
  }

  // 完整加载所有水印（包含图像解码）
  Future<void> loadAllWatermarks() async {
    _isLoadingWatermarks = true;
    _totalWatermarks = _watermarks.length;
    _loadedWatermarks = 0;

    print('🖼️ 开始加载所有PNG水印...');

    for (int i = 0; i < _watermarks.length; i++) {
      final watermark = _watermarks[i];
      _currentLoadingWatermark = watermark.name;

      // 调用进度回调
      _progressCallback?.call(
        _loadedWatermarks,
        _totalWatermarks,
        _currentLoadingWatermark,
      );

      await loadWatermark(watermark);

      _loadedWatermarks++;

      // 调用进度回调
      _progressCallback?.call(
        _loadedWatermarks,
        _totalWatermarks,
        _currentLoadingWatermark,
      );
    }

    _isLoadingWatermarks = false;
    _currentLoadingWatermark = '';
    print('✅ 所有PNG水印加载完成');
  }

  // 延迟解码水印图像（从缓存数据解码）
  Future<bool> decodeWatermarkFromCache(PngWatermark watermark) async {
    try {
      if (watermark.imageData == null) {
        // 如果内存中没有数据，从缓存文件加载
        if (watermark.cacheFilePath != null) {
          final cacheFile = File(watermark.cacheFilePath!);
          if (await cacheFile.exists()) {
            watermark.imageData = await cacheFile.readAsBytes();
          } else {
            print('❌ 缓存文件不存在: ${watermark.name}');
            return false;
          }
        } else {
          print('❌ 水印数据和缓存路径都不存在: ${watermark.name}');
          return false;
        }
      }

      // 解码为ui.Image用于Flutter显示
      final codec = await ui.instantiateImageCodec(watermark.imageData!);
      final frame = await codec.getNextFrame();
      watermark.uiImage = frame.image;
      watermark.originalSize = Size(
        frame.image.width.toDouble(),
        frame.image.height.toDouble(),
      );

      // 解码为img.Image用于图像处理
      watermark.processedImage = img.decodeImage(watermark.imageData!);

      print('✅ 水印解码完成: ${watermark.name}');
      return true;
    } catch (e) {
      print('❌ 水印解码失败: $e');
      return false;
    }
  }

  // 缩放水印以适应屏幕宽度
  Future<void> scaleWatermarkToFitScreen(
    PngWatermark watermark,
    double appWidth,
  ) async {
    if (watermark.imageData == null || watermark.originalSize == null) {
      print('❌ 水印数据不完整，无法缩放');
      return;
    }

    final originalWidth = watermark.originalSize!.width;
    final originalHeight = watermark.originalSize!.height;

    // 检查是否需要缩放
    if (originalWidth <= appWidth) {
      print(
        '✅ 水印无需缩放: ${watermark.name} (${originalWidth.toInt()} <= ${appWidth.toInt()})',
      );
      return;
    }

    try {
      // 计算缩放比例
      double scaleFactor = appWidth / originalWidth;
      double newWidth = appWidth;
      double newHeight = originalHeight * scaleFactor;

      // 确保新尺寸有效
      if (newWidth <= 0 || newHeight <= 0) {
        print('❌ 计算的新尺寸无效: ${newWidth} x ${newHeight}');
        return;
      }

      print('🔄 缩放水印: ${watermark.name}');
      print('   原始尺寸: ${originalWidth.toInt()} x ${originalHeight.toInt()}');
      print('   缩放后尺寸: ${newWidth.toInt()} x ${newHeight.toInt()}');
      print('   缩放比例: ${scaleFactor.toStringAsFixed(2)}');

      // 重新编码为指定尺寸
      final resizedCodec = await ui.instantiateImageCodec(
        watermark.imageData!,
        targetWidth: newWidth.toInt(),
        targetHeight: newHeight.toInt(),
      );
      final resizedFrame = await resizedCodec.getNextFrame();

      // 更新水印图像和尺寸
      watermark.uiImage = resizedFrame.image;
      watermark.originalSize = Size(newWidth, newHeight);

      print('✅ 水印缩放完成: ${watermark.name}');
    } catch (e) {
      print('❌ 水印缩放失败: $e');
    }
  }

  // 强制刷新水印（忽略缓存）
  Future<bool> refreshWatermark(PngWatermark watermark) async {
    try {
      print('🔄 强制刷新水印: ${watermark.name}');

      // 清除现有数据
      watermark.imageData = null;
      watermark.uiImage = null;
      watermark.processedImage = null;
      watermark.isLoaded = false;

      // 删除缓存文件
      if (watermark.cacheFilePath != null) {
        final cacheFile = File(watermark.cacheFilePath!);
        if (await cacheFile.exists()) {
          await cacheFile.delete();
          print('🗑️ 删除旧缓存: ${watermark.name}');
        }
      }

      // 重新从网络加载
      final response = await http.get(Uri.parse(watermark.url));
      if (response.statusCode == 200) {
        watermark.imageData = response.bodyBytes;

        // 解码为ui.Image用于Flutter显示
        final codec = await ui.instantiateImageCodec(watermark.imageData!);
        final frame = await codec.getNextFrame();
        watermark.uiImage = frame.image;
        watermark.originalSize = Size(
          frame.image.width.toDouble(),
          frame.image.height.toDouble(),
        );

        // 解码为img.Image用于图像处理
        watermark.processedImage = img.decodeImage(watermark.imageData!);

        // 保存到缓存
        await _saveToCache(watermark);

        watermark.isLoaded = true;
        print('✅ 水印刷新成功: ${watermark.name}');
        return true;
      } else {
        print('❌ 水印刷新失败: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('❌ 水印刷新错误: $e');
      return false;
    }
  }

  // 刷新所有水印
  Future<void> refreshAllWatermarks() async {
    print('🔄 开始刷新所有PNG水印...');
    for (final watermark in _watermarks) {
      await refreshWatermark(watermark);
    }
    print('✅ 所有PNG水印刷新完成');
  }

  // 清理缓存
  Future<void> clearCache() async {
    try {
      await _initCacheDirectory();

      if (await _cacheDirectory!.exists()) {
        final files = await _cacheDirectory!.list().toList();
        for (final file in files) {
          if (file is File) {
            await file.delete();
          }
        }
        print('🗑️ 缓存清理完成');
      }

      // 重置所有水印的缓存信息
      for (final watermark in _watermarks) {
        watermark.cacheFilePath = null;
        watermark.lastCacheTime = null;
        watermark.isLoaded = false;
      }
    } catch (e) {
      print('❌ 缓存清理失败: $e');
    }
  }

  // 获取缓存大小
  Future<int> getCacheSize() async {
    try {
      await _initCacheDirectory();

      if (!await _cacheDirectory!.exists()) {
        return 0;
      }

      int totalSize = 0;
      final files = await _cacheDirectory!.list().toList();
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          totalSize += stat.size;
        }
      }
      return totalSize;
    } catch (e) {
      print('❌ 获取缓存大小失败: $e');
      return 0;
    }
  }

  // 计算水印在屏幕上的显示尺寸和位置（优化后的排版规则）
  Rect calculateWatermarkRect(Size screenSize, PngWatermark watermark) {
    if (watermark.originalSize == null) {
      return Rect.zero;
    }

    final originalSize = watermark.originalSize!;
    final aspectRatio = originalSize.width / originalSize.height;

    double width, height;
    double left = 0, top = 0;

    // 新的排版规则：水印宽度与相机预览宽度保持一致
    // 如果高度超出相机预览高度，则让高度保持与相机预览高度一致，等比调整宽度
    width = screenSize.width;
    height = width / aspectRatio;

    // 如果计算出的高度超过屏幕高度，则以高度为准重新计算
    if (height > screenSize.height) {
      height = screenSize.height;
      width = height * aspectRatio;
      // 水平居中
      left = (screenSize.width - width) / 2;
    } else {
      // 垂直居中
      top = (screenSize.height - height) / 2;
    }

    print(
      '📐 水印显示尺寸计算: 原始=${originalSize.width.toInt()}x${originalSize.height.toInt()}, 显示=${width.toInt()}x${height.toInt()}',
    );

    return Rect.fromLTWH(left, top, width, height);
  }

  // 计算相机预览区域（完整显示相机画面）
  Rect calculateCameraPreviewRect(Size screenSize, PngWatermark? watermark) {
    // 始终返回全屏尺寸，让相机完整显示
    return Rect.fromLTWH(0, 0, screenSize.width, screenSize.height);
  }

  // 将PNG水印叠加到图像上（新的排版规则，支持缩放、旋转和镜像翻转）
  img.Image? applyWatermarkToImage(
    img.Image originalImage,
    PngWatermark watermark, {
    Size? previewSize, // 预览区域尺寸，用于计算比例
  }) {
    if (watermark.processedImage == null) {
      print('⚠️ PNG水印未加载，无法应用到图像');
      return originalImage;
    }

    try {
      // 获取水印原始尺寸
      img.Image processedWatermark = watermark.processedImage!;

      // 应用镜像翻转
      if (watermark.flipHorizontal) {
        processedWatermark = img.flipHorizontal(processedWatermark);
      }
      if (watermark.flipVertical) {
        processedWatermark = img.flipVertical(processedWatermark);
      }

      final watermarkAspectRatio =
          processedWatermark.width / processedWatermark.height;
      final imageAspectRatio = originalImage.width / originalImage.height;

      int baseWidth, baseHeight;

      // 优化后的排版规则：确保渲染时使用原图质量
      // 水印宽度与相机画面宽度保持一致，如果高度超出则等比调整
      baseWidth = originalImage.width;
      baseHeight = (baseWidth / watermarkAspectRatio).round();

      // 如果计算出的高度超过图像高度，则以高度为准重新计算
      if (baseHeight > originalImage.height) {
        baseHeight = originalImage.height;
        baseWidth = (baseHeight * watermarkAspectRatio).round();
        print('📐 高度限制模式：水印高度匹配相机高度 ${baseWidth}x${baseHeight}');
      } else {
        print('📐 宽度优先模式：水印宽度匹配相机宽度 ${baseWidth}x${baseHeight}');
      }

      // 应用用户设置的缩放
      final finalWidth = (baseWidth * watermark.scale).round();
      final finalHeight = (baseHeight * watermark.scale).round();

      // 使用高质量算法调整水印尺寸
      img.Image resizedWatermark = img.copyResize(
        processedWatermark,
        width: finalWidth,
        height: finalHeight,
        interpolation: img.Interpolation.cubic, // 使用立方插值获得更好的质量
      );

      // 保存旋转前的尺寸用于位置计算
      final originalWatermarkWidth = resizedWatermark.width;
      final originalWatermarkHeight = resizedWatermark.height;

      // 如果有旋转角度，应用旋转
      if (watermark.rotation != 0.0) {
        final rotationDegrees = watermark.rotation * 180 / 3.14159; // 弧度转角度
        print('🔄 应用水印旋转: ${rotationDegrees.toStringAsFixed(1)}度');
        resizedWatermark = img.copyRotate(
          resizedWatermark,
          angle: rotationDegrees,
        );
        print(
          '📐 旋转后水印尺寸: ${resizedWatermark.width}x${resizedWatermark.height}',
        );
      }

      // 计算位置（使用旋转前的尺寸进行位置计算，避免旋转后位置偏移）
      final centerX = (originalImage.width - originalWatermarkWidth) ~/ 2;
      final centerY = (originalImage.height - originalWatermarkHeight) ~/ 2;

      // 计算预览尺寸和实际图像尺寸的比例
      double scaleX = 1.0;
      double scaleY = 1.0;

      if (previewSize != null) {
        scaleX = originalImage.width / previewSize.width;
        scaleY = originalImage.height / previewSize.height;
        print('📐 尺寸比例: scaleX=$scaleX, scaleY=$scaleY');
      }

      // 应用用户设置的偏移（考虑预览和实际图像的比例差异）
      // 使用旋转前的尺寸计算最大偏移量，确保位置一致性
      final maxOffsetX = (originalImage.width - originalWatermarkWidth) ~/ 2;
      final maxOffsetY = (originalImage.height - originalWatermarkHeight) ~/ 2;

      // 将预览中的偏移量转换为实际图像中的偏移量
      final scaledOffsetX = watermark.offsetX * scaleX;
      final scaledOffsetY = watermark.offsetY * scaleY;

      // 计算最终位置，然后调整以适应旋转后的实际尺寸
      var offsetX = centerX + (scaledOffsetX * maxOffsetX).round();
      var offsetY = centerY + (scaledOffsetY * maxOffsetY).round();

      // 如果水印被旋转了，需要调整位置以确保旋转后的水印正确居中
      if (watermark.rotation != 0.0) {
        final rotatedCenterX =
            (originalImage.width - resizedWatermark.width) ~/ 2;
        final rotatedCenterY =
            (originalImage.height - resizedWatermark.height) ~/ 2;

        // 调整偏移以适应旋转后的新尺寸
        final centerOffsetX = offsetX - centerX;
        final centerOffsetY = offsetY - centerY;

        offsetX = rotatedCenterX + centerOffsetX;
        offsetY = rotatedCenterY + centerOffsetY;
      }

      print(
        '📍 最终位置: offsetX=$offsetX, offsetY=$offsetY (原始偏移: ${watermark.offsetX}, ${watermark.offsetY})',
      );

      // 叠加水印
      img.compositeImage(
        originalImage,
        resizedWatermark,
        dstX: offsetX,
        dstY: offsetY,
      );

      print(
        '✅ PNG水印已应用到图像 (缩放: ${watermark.scale}, 旋转: ${watermark.rotation}, 水平翻转: ${watermark.flipHorizontal}, 垂直翻转: ${watermark.flipVertical})',
      );
      return originalImage;
    } catch (e) {
      print('❌ 应用PNG水印到图像时出错: $e');
      return originalImage;
    }
  }
}
