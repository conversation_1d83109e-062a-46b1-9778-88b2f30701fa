import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:camera/camera.dart';
import 'package:camera_android_camerax/camera_android_camerax.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:image/image.dart' as img;
import 'package:intl/intl.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:image_picker/image_picker.dart';
import 'gallery_page.dart';
import 'png_watermark_manager.dart';
import 'png_menu_page.dart';
import 'camera_lens_manager.dart';
import 'widgets/camera_controls_panel.dart';
import 'widgets/lens_selection_panel.dart';
import 'widgets/top_menu_panel.dart';

List<CameraDescription> cameras = [];

// 相机优化方法通道
const MethodChannel _cameraOptimizationChannel = MethodChannel(
  'com.example.watermark_camera/camera_optimization',
);

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 强制Android使用CameraX实现
  if (Platform.isAndroid) {
    AndroidCameraCameraX.registerWith();
    print('📱 Android平台 - 已注册CameraX实现');
    print('📱 CameraX插件版本: camera_android_camerax ^0.6.19');
  }

  // 相机列表将在权限获取成功后获取
  print('🚀 应用启动 - 开始初始化');

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '透卡相机',
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      home: const CameraScreen(),
    );
  }
}

class CameraScreen extends StatefulWidget {
  const CameraScreen({super.key});

  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  CameraController? _controller;
  bool _isInitialized = false;
  bool _isTakingPicture = false;
  final ImagePicker _picker = ImagePicker();
  bool _isAppInBackground = false;
  final PngWatermarkManager _watermarkManager = PngWatermarkManager();
  final CameraLensManager _lensManager = CameraLensManager();
  bool _isLoadingMenu = false;
  bool _isDisposing = false; // 新增：标记是否正在释放资源

  // 初始化锁和状态跟踪变量
  bool _isInitializing = false; // 防止并发初始化
  bool _hasInitializedOnce = false; // 跟踪是否已经初始化过

  // 摄像头切换相关变量
  int _currentCameraIndex = 0; // 当前使用的摄像头索引（0=后置，1=前置）
  bool _isSwitchingCamera = false;

  // 相机控制状态
  double _currentZoom = 1.0; // 当前缩放级别
  double _minZoom = 1.0; // 最小缩放
  double _maxZoom = 1.0; // 最大缩放
  FlashMode _flashMode = FlashMode.auto; // 闪光灯模式
  double _exposureOffset = 0.0; // 曝光补偿
  double _minExposure = 0.0; // 最小曝光
  double _maxExposure = 0.0; // 最大曝光
  bool _showCameraControls = false; // 是否显示相机控制面板

  // 新UI状态变量
  bool _showTopMenu = false; // 是否显示右上角菜单
  bool _showLensSelection = false; // 是否显示镜头选择面板
  bool _isLensExpanded = false; // 顶部标题是否展开

  // 手势控制相关变量
  double _initialScale = 1.0;
  double _initialRotation = 0.0;

  // 聚焦环相关变量
  bool _showFocusRing = false; // 是否显示聚焦环
  Offset _focusPosition = Offset.zero; // 聚焦环位置
  late AnimationController _focusAnimationController;
  late Animation<double> _focusAnimation;

  // 水印操作相关状态
  double _watermarkInitialScale = 1.0;
  double _watermarkInitialRotation = 0.0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // 初始化聚焦环动画控制器
    _focusAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _focusAnimation = Tween<double>(begin: 1.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _focusAnimationController,
        curve: Curves.easeOutBack,
      ),
    );

    _requestPermissions();
  }

  Future<void> _requestPermissions() async {
    print('📱 开始权限检查流程');

    try {
      // 获取设备信息
      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        print(
          '📱 Android设备信息: API ${androidInfo.version.sdkInt}, 型号: ${androidInfo.model}',
        );
      }

      // 检查当前相机权限状态
      final currentCameraStatus = await Permission.camera.status;
      print('📷 当前相机权限状态: $currentCameraStatus');

      // 请求相机权限
      print('📷 正在请求相机权限...');
      final cameraStatus = await Permission.camera.request();
      print('📷 相机权限请求结果: $cameraStatus');

      // 对于Android 10及以上，需要特殊处理存储权限
      bool storageGranted = false;
      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        print('💾 开始处理存储权限 (Android API ${androidInfo.version.sdkInt})');

        if (androidInfo.version.sdkInt >= 30) {
          // Android 11及以上：优先检查基本存储权限，MANAGE_EXTERNAL_STORAGE为可选
          print('💾 Android 11+: 检查存储权限');

          // 首先检查基本的存储权限
          final writeStatus = await Permission.storage.status;
          print('💾 WRITE_EXTERNAL_STORAGE权限状态: $writeStatus');

          if (writeStatus.isGranted) {
            storageGranted = true;
            print('💾 基本存储权限已授予');
          } else {
            // 尝试请求基本存储权限
            final requestedWriteStatus = await Permission.storage.request();
            print('💾 请求WRITE_EXTERNAL_STORAGE权限结果: $requestedWriteStatus');

            if (requestedWriteStatus.isGranted) {
              storageGranted = true;
            } else {
              // 基本权限未授予，检查MANAGE_EXTERNAL_STORAGE
              final manageStorageStatus =
                  await Permission.manageExternalStorage.status;
              print('💾 MANAGE_EXTERNAL_STORAGE权限状态: $manageStorageStatus');

              if (manageStorageStatus.isGranted) {
                storageGranted = true;
                print('💾 MANAGE_EXTERNAL_STORAGE权限已授予');
              } else {
                // 即使没有完整的存储权限，也允许使用应用私有目录
                print('💾 存储权限未完全授予，将使用应用私有目录');
                storageGranted = true; // 允许继续使用
              }
            }
          }
        } else if (androidInfo.version.sdkInt >= 29) {
          // Android 10使用Scoped Storage，不需要存储权限
          print('💾 Android 10: 使用Scoped Storage，无需额外存储权限');
          storageGranted = true;
        } else {
          // Android 9及以下需要存储权限
          print('💾 Android 9及以下: 请求传统存储权限');
          final currentStorageStatus = await Permission.storage.status;
          print('💾 当前存储权限状态: $currentStorageStatus');

          final storageStatus = await Permission.storage.request();
          print('💾 存储权限请求结果: $storageStatus');
          storageGranted = storageStatus.isGranted;
        }
      } else {
        // iOS需要相册权限
        print('💾 iOS: 请求相册权限');
        final currentPhotosStatus = await Permission.photos.status;
        print('💾 当前相册权限状态: $currentPhotosStatus');

        final photosStatus = await Permission.photos.request();
        print('💾 相册权限请求结果: $photosStatus');
        storageGranted = photosStatus.isGranted;
      }

      // 权限检查结果汇总
      print('✅ 权限检查完成:');
      print('   📷 相机权限: ${cameraStatus.isGranted ? "已授权" : "未授权"}');
      print('   💾 存储权限: ${storageGranted ? "已授权" : "未授权"}');

      if (cameraStatus.isGranted) {
        print('🎉 相机权限已获得，开始初始化相机');

        // 确保在主线程中初始化相机
        if (mounted) {
          await _initializeCamera();
        }

        if (!storageGranted && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('存储权限未授予，照片将保存到应用私有目录'),
              duration: Duration(seconds: 3),
            ),
          );
        }
      } else if (cameraStatus.isPermanentlyDenied) {
        // 相机权限被永久拒绝，必须引导用户去设置
        print('❌ 相机权限被永久拒绝，需要用户手动开启');
        if (mounted) {
          showDialog(
            context: context,
            builder: (BuildContext context) => AlertDialog(
              title: const Text('需要相机权限'),
              content: const Text('应用需要相机权限才能拍照，请在设置中开启相机权限'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    openAppSettings();
                  },
                  child: const Text('去设置'),
                ),
              ],
            ),
          );
        }
      } else {
        // 相机权限被临时拒绝，显示重试选项
        print('⚠️ 相机权限被拒绝，显示重试选项');
        if (mounted) {
          showDialog(
            context: context,
            builder: (BuildContext context) => AlertDialog(
              title: const Text('需要相机权限'),
              content: const Text('应用需要相机权限才能正常工作，请允许相机权限'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _requestPermissions(); // 重新请求权限
                  },
                  child: const Text('重试'),
                ),
              ],
            ),
          );
        }
      }
    } catch (e) {
      print('❌ 权限请求过程中出错: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('权限请求失败: $e')));
      }
    }
  }

  Future<void> _initializeCamera() async {
    // 防止并发初始化
    if (_isInitializing) {
      print('⚠️ 相机正在初始化中，跳过重复初始化');
      return;
    }

    _isInitializing = true;

    try {
      print('📷 开始获取可用相机列表...');
      if (Platform.isAndroid) {
        print('📱 Android平台 - 使用CameraX实现');
      }

      // 确保在获取相机列表前有足够的延迟
      await Future.delayed(const Duration(milliseconds: 500));

      cameras = await availableCameras();
      print('📷 发现 ${cameras.length} 个相机设备');

      for (int i = 0; i < cameras.length; i++) {
        print('   相机 $i: ${cameras[i].name} (${cameras[i].lensDirection})');
      }

      if (cameras.isEmpty) {
        print('❌ 没有发现可用的相机设备');
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('没有发现可用的相机设备')));
        }
        _isInitializing = false;
        return;
      }

      // 初始化镜头管理器
      await _lensManager.initializeLenses();
    } catch (e) {
      print('❌ 获取相机列表失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('获取相机列表失败: $e')));
      }
      _isInitializing = false;
      return;
    }

    // 检查是否正在释放资源
    if (_isDisposing) {
      print('⚠️ 正在释放资源，取消初始化');
      _isInitializing = false;
      return;
    }

    print('📷 正在初始化相机控制器...');

    // 确保使用有效的相机索引
    if (_currentCameraIndex >= cameras.length) {
      _currentCameraIndex = 0;
    }

    _controller = CameraController(
      cameras[_currentCameraIndex], // 使用当前选择的摄像头
      ResolutionPreset.high, // 预览使用高分辨率，减少缓冲区压力
      enableAudio: false, // 禁用音频
      imageFormatGroup: ImageFormatGroup.jpeg, // 预览使用JPEG格式，减少内存占用
    );

    try {
      print('📷 开始初始化相机控制器...');
      await _controller!.initialize();

      // 再次检查是否正在释放资源
      if (_isDisposing || !mounted) {
        print('⚠️ 初始化过程中检测到释放操作，清理资源');
        await _controller?.dispose();
        _controller = null;
        _isInitializing = false;
        return;
      }

      // 配置CameraX优化设置（JPEG预览模式）
      if (Platform.isAndroid) {
        print('📱 配置CameraX优化设置（JPEG预览模式）...');

        // JPEG预览模式减少缓冲区压力
        try {
          // 使用JPEG格式预览，显著减少内存占用和缓冲区压力
          // 注意：某些设备可能不支持帧率设置，使用try-catch保护
          // await _controller!.setFPS(30); // 如果API支持的话

          // 添加延迟以确保相机完全初始化
          await Future.delayed(const Duration(milliseconds: 500));

          print('📱 CameraX缓冲区优化已应用（JPEG预览模式）');
        } catch (e) {
          print('⚠️ 帧率设置不支持，使用默认配置: $e');
        }

        // 调用Android原生优化方法
        try {
          await _cameraOptimizationChannel.invokeMethod(
            'optimizeCameraBuffers',
          );
          print('📱 Android原生缓冲区优化完成');
        } catch (e) {
          print('⚠️ Android原生优化失败: $e');
        }

        // 强制垃圾回收以释放内存
        try {
          // 这有助于减少内存压力，间接缓解缓冲区问题
          await Future.delayed(const Duration(milliseconds: 100));
          print('📱 内存优化完成');
        } catch (e) {
          print('⚠️ 内存优化失败: $e');
        }
      }

      // 等待一小段时间确保相机完全准备好
      await Future.delayed(const Duration(milliseconds: 500));

      print('✅ 相机控制器初始化成功');

      // 获取相机能力参数
      await _initializeCameraCapabilities();

      if (mounted && !_isDisposing) {
        setState(() {
          _isInitialized = true;
        });
        print('🎉 相机界面已更新，_isInitialized = $_isInitialized');
        _hasInitializedOnce = true;
      }
    } catch (e) {
      print('❌ 相机控制器初始化错误: $e');

      // 清理失败的控制器
      if (_controller != null) {
        try {
          await _controller!.dispose();
        } catch (disposeError) {
          print('⚠️ 清理失败的控制器时出错: $disposeError');
        }
        _controller = null;
      }

      if (mounted && !_isDisposing) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('相机初始化失败: $e')));

        // 显示一个简单的错误界面而不是空白
        setState(() {
          _isInitialized = false;
        });
      }
    } finally {
      _isInitializing = false;
    }
  }

  Future<void> _takePicture() async {
    if (!_isCameraReady || _isTakingPicture) {
      print('⚠️ 相机未准备好或正在拍照中');
      return;
    }

    // 检查相机状态
    if (_controller!.value.hasError) {
      print('❌ 相机状态异常，尝试重新初始化...');
      await _reinitializeCamera();
      return;
    }

    setState(() {
      _isTakingPicture = true;
    });

    try {
      print('📸 开始拍照...');
      print(
        '📸 相机状态: 已初始化=${_controller!.value.isInitialized}, 预览暂停=${_controller!.value.isPreviewPaused}',
      );

      // 确保预览没有暂停
      if (_controller!.value.isPreviewPaused) {
        print('📷 预览已暂停，恢复预览...');
        await _controller!.resumePreview();
        await Future.delayed(const Duration(milliseconds: 200));
      }

      // 再次检查相机状态
      if (!_isCameraReady) {
        print('⚠️ 拍照前相机状态异常');
        return;
      }

      // 拍照 - 使用高质量模式
      print('📸 准备拍照，使用当前预览配置（高分辨率JPEG格式）');
      await Future.delayed(const Duration(milliseconds: 50)); // 确保相机稳定
      final XFile image = await _controller!.takePicture();
      print('📸 拍照完成: ${image.path}');

      // 立即恢复相机预览，避免卡在最后一帧
      await _ensureCameraPreview();

      // 添加短暂延迟释放缓冲区
      await Future.delayed(const Duration(milliseconds: 100));

      // 在后台处理水印和保存，不阻塞UI
      _processImageInBackground(image.path);
    } catch (e) {
      print('❌ 拍照错误: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('拍照失败: $e')));
      }

      // 发生错误时也要确保相机预览恢复
      await _ensureCameraPreview();
    } finally {
      if (mounted) {
        setState(() {
          _isTakingPicture = false;
        });
      }
      print('📸 拍照流程完成');
    }
  }

  // 确保相机预览正常工作
  Future<void> _ensureCameraPreview() async {
    try {
      if (!_isCameraReady) {
        print('⚠️ 相机未准备好，无法恢复预览');
        return;
      }

      if (_controller != null &&
          _controller!.value.isInitialized &&
          !_isDisposing) {
        // 强制刷新预览状态，解决拍照后卡在最后一帧的问题
        print('📷 强制刷新相机预览状态...');

        // 多重恢复策略
        try {
          // 策略1: 暂停和恢复预览
          await _controller!.pausePreview();
          await Future.delayed(const Duration(milliseconds: 300));

          if (_isCameraReady && !_isDisposing) {
            await _controller!.resumePreview();
            await Future.delayed(const Duration(milliseconds: 200));
          }

          // 策略2: 重新设置相机参数强制刷新
          if (_isCameraReady && !_isDisposing) {
            // 重新设置缩放级别来强制刷新
            await _controller!.setZoomLevel(_currentZoom);

            // 重新设置闪光灯模式来强制刷新
            await _controller!.setFlashMode(_flashMode);

            await Future.delayed(const Duration(milliseconds: 100));
          }

          print('📷 相机预览已刷新');

          // 强制UI完全重建
          if (mounted) {
            setState(() {
              // 强制重建整个UI
            });
          }
        } catch (refreshError) {
          print('⚠️ 预览刷新失败，尝试重新初始化: $refreshError');
          // 如果刷新失败，直接重新初始化
          await _reinitializeCamera();
        }
      }
    } catch (e) {
      print('⚠️ 恢复相机预览时出错: $e');
      // 如果恢复失败，尝试重新初始化相机
      if (!_isDisposing && mounted) {
        await _reinitializeCamera();
      }
    }
  }

  // 重新初始化相机
  Future<void> _reinitializeCamera() async {
    // 防止重复初始化
    if (_isDisposing || _isInitializing) {
      print('⚠️ 正在释放资源或初始化中，取消重新初始化');
      return;
    }

    _isInitializing = true;

    try {
      print('🔄 重新初始化相机...');

      // 确保先清理现有资源
      if (_controller != null) {
        try {
          _isDisposing = true;
          await _controller!.dispose();
        } catch (e) {
          print('⚠️ 清理旧相机控制器时出错: $e');
        } finally {
          _controller = null;
          _isDisposing = false;
        }
      }

      if (!mounted) {
        print('⚠️ Widget已卸载，取消初始化');
        return;
      }

      setState(() {
        _isInitialized = false;
      });

      // 等待一小段时间确保资源完全释放
      await Future.delayed(const Duration(milliseconds: 300));

      if (cameras.isNotEmpty && mounted && !_isDisposing) {
        _controller = CameraController(
          cameras[_currentCameraIndex],
          ResolutionPreset.high, // 预览使用高分辨率，减少缓冲区压力
          enableAudio: false,
          imageFormatGroup: ImageFormatGroup.jpeg, // 预览使用JPEG格式，减少内存占用
        );

        await _controller!.initialize();

        // 再次检查状态
        if (!mounted || _isDisposing) {
          print('⚠️ 初始化过程中状态改变，清理资源');
          await _controller?.dispose();
          _controller = null;
          return;
        }

        // 再等待一小段时间确保相机完全准备好
        await Future.delayed(const Duration(milliseconds: 200));

        if (mounted && !_isDisposing) {
          setState(() {
            _isInitialized = true;
          });
        }

        print('✅ 相机重新初始化成功');
        _hasInitializedOnce = true;
      }
    } catch (e) {
      print('❌ 重新初始化相机失败: $e');
      // 如果重新初始化失败，再次尝试
      if (mounted && !_isDisposing) {
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted && !_isInitialized && !_isDisposing && !_isInitializing) {
            print('🔄 重试相机初始化...');
            _reinitializeCamera();
          }
        });
      }
    } finally {
      _isInitializing = false;
    }
  }

  // 在后台处理图片水印和保存（优化性能）
  void _processImageInBackground(String imagePath) async {
    try {
      // 立即显示处理中的提示，提升用户体验
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('正在处理照片...'),
            duration: Duration(seconds: 1),
          ),
        );
      }

      // 使用compute在独立isolate中处理图像，避免阻塞UI
      final String? savedPath = await _addWatermarkAndSaveOptimized(imagePath);

      if (mounted) {
        if (savedPath != null) {
          print('💾 照片保存成功: $savedPath');
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('照片已保存到相册')));
        } else {
          print('❌ 照片保存失败');
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('照片保存失败')));
        }
      }
    } catch (e) {
      print('❌ 后台处理图片失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('照片处理失败: $e')));
      }
    }
  }

  // 优化的图像处理方法，减少内存使用和处理时间
  Future<String?> _addWatermarkAndSaveOptimized(String imagePath) async {
    try {
      // 异步读取图片，避免阻塞UI
      final bytes = await File(imagePath).readAsBytes();
      final img.Image? image = img.decodeImage(bytes);

      if (image == null) return null;

      // 优化：只有在有水印时才进行处理
      final selectedWatermark = _watermarkManager.selectedWatermark;
      img.Image finalImage = image;

      if (selectedWatermark != null) {
        // 获取当前预览尺寸用于位置计算
        final screenSize = MediaQuery.of(context).size;
        Size actualPreviewSize;

        if (_controller != null && _controller!.value.isInitialized) {
          final aspectRatio = _controller!.value.aspectRatio;
          if (aspectRatio > screenSize.width / screenSize.height) {
            actualPreviewSize = Size(
              screenSize.width,
              screenSize.width / aspectRatio,
            );
          } else {
            actualPreviewSize = Size(
              screenSize.height * aspectRatio,
              screenSize.height,
            );
          }
        } else {
          actualPreviewSize = screenSize;
        }

        // 应用水印
        final watermarkedImage = _watermarkManager.applyWatermarkToImage(
          image,
          selectedWatermark,
          previewSize: actualPreviewSize,
        );

        if (watermarkedImage != null) {
          finalImage = watermarkedImage;
        }
      }

      // 异步保存图片
      return await _saveImageToGallery(finalImage, imagePath);
    } catch (e) {
      print('优化图像处理错误: $e');
      return null;
    }
  }

  // 优化的图片保存方法
  Future<String?> _saveImageToGallery(
    img.Image image,
    String originalPath,
  ) async {
    try {
      // 获取保存目录
      Directory? picturesDir;
      String savePath;

      if (Platform.isAndroid) {
        // Android: 在DCIM目录下创建专门的透卡相机文件夹
        final externalDir = await getExternalStorageDirectory();
        if (externalDir != null) {
          final dcimPath = externalDir.path.replaceAll(
            '/Android/data/com.example.watermark_camera/files',
            '/DCIM/TransparentCard',
          );
          picturesDir = Directory(dcimPath);
          print('📁 保存目录: $dcimPath');
        }
      } else {
        // iOS: 在文档目录下创建透卡相机文件夹
        final documentsDir = await getApplicationDocumentsDirectory();
        picturesDir = Directory(
          path.join(documentsDir.path, 'TransparentCard'),
        );
      }

      if (picturesDir == null) {
        print('❌ 无法获取保存目录');
        return null;
      }

      // 确保目录存在
      if (!await picturesDir.exists()) {
        await picturesDir.create(recursive: true);
        print('📁 创建保存目录: ${picturesDir.path}');
      }

      // 生成文件名
      final now = DateTime.now();
      final formatter = DateFormat('yyyyMMdd_HHmmss');
      final fileName = 'TransparentCard_${formatter.format(now)}.jpg';
      savePath = path.join(picturesDir.path, fileName);

      print('💾 保存路径: $savePath');

      // 使用高质量JPEG编码保存
      await File(savePath).writeAsBytes(
        img.encodeJpg(image, quality: 95), // 使用95%质量保存
      );
      print('✅ 照片已保存到: $savePath');

      // 删除原始图片
      await File(originalPath).delete();

      return savePath;
    } catch (e) {
      print('保存图片错误: $e');
      return null;
    }
  }

  // 原始的图像处理方法（保留作为备用）
  Future<String?> _addWatermarkAndSave(String imagePath) async {
    try {
      // 读取图片
      final bytes = await File(imagePath).readAsBytes();
      final img.Image? image = img.decodeImage(bytes);

      if (image == null) return null;

      // 添加水印
      final watermarkedImage = _addWatermark(image);

      // 获取真正的相册目录（DCIM/Camera）
      Directory? picturesDir;
      String savePath;

      if (Platform.isAndroid) {
        // Android: 在DCIM目录下创建专门的透卡相机文件夹
        final externalDir = await getExternalStorageDirectory();
        if (externalDir != null) {
          // 构建 DCIM/TransparentCard 路径（在相册中创建专门文件夹）
          final dcimPath = externalDir.path.replaceAll(
            '/Android/data/com.example.watermark_camera/files',
            '/DCIM/TransparentCard',
          );
          picturesDir = Directory(dcimPath);
          print('📁 保存目录: $dcimPath');
        }
      } else {
        // iOS: 在文档目录下创建透卡相机文件夹
        final documentsDir = await getApplicationDocumentsDirectory();
        picturesDir = Directory(
          path.join(documentsDir.path, 'TransparentCard'),
        );
      }

      if (picturesDir == null) {
        print('❌ 无法获取保存目录');
        return null;
      }

      // 确保目录存在
      if (!await picturesDir.exists()) {
        await picturesDir.create(recursive: true);
        print('📁 创建保存目录: ${picturesDir.path}');
      }

      // 生成文件名（使用更友好的格式）
      final now = DateTime.now();
      final formatter = DateFormat('yyyyMMdd_HHmmss');
      final fileName = 'TransparentCard_${formatter.format(now)}.jpg';
      savePath = path.join(picturesDir.path, fileName);

      print('💾 保存路径: $savePath');

      // 保存带水印的图片
      await File(savePath).writeAsBytes(img.encodeJpg(watermarkedImage));
      print('✅ 照片已保存到: $savePath');

      // 删除原始图片
      await File(imagePath).delete();

      return savePath;
    } catch (e) {
      print('添加水印错误: $e');
      return null;
    }
  }

  // 切换前后置摄像头
  Future<void> _switchCamera() async {
    if (_isSwitchingCamera || cameras.length < 2) return;

    setState(() {
      _isSwitchingCamera = true;
    });

    try {
      // 切换到下一个摄像头
      _currentCameraIndex = (_currentCameraIndex + 1) % cameras.length;

      print(
        '📷 切换到摄像头: ${cameras[_currentCameraIndex].name} (${cameras[_currentCameraIndex].lensDirection})',
      );

      // 重新初始化相机
      await _reinitializeCamera();
    } catch (e) {
      print('❌ 切换摄像头失败: $e');
      // 如果切换失败，回退到原来的摄像头
      _currentCameraIndex = (_currentCameraIndex - 1) % cameras.length;
    } finally {
      setState(() {
        _isSwitchingCamera = false;
      });
    }
  }

  // 切换到指定镜头
  Future<void> _switchToLens(int lensIndex) async {
    if (_isSwitchingCamera ||
        lensIndex >= _lensManager.availableLenses.length) {
      return;
    }

    setState(() {
      _isSwitchingCamera = true;
    });

    try {
      // 使用镜头管理器切换镜头
      final success = _lensManager.switchToLens(lensIndex);

      if (success) {
        // 更新当前相机索引
        final selectedLens = _lensManager.availableLenses[lensIndex];
        _currentCameraIndex = cameras.indexWhere(
          (camera) => camera == selectedLens.camera,
        );

        print('📷 切换到镜头: ${selectedLens.displayName}');

        // 重新初始化相机
        await _reinitializeCamera();
      } else {
        print('❌ 镜头切换失败：无效的镜头索引');
      }
    } catch (e) {
      print('❌ 切换镜头失败: $e');
    } finally {
      setState(() {
        _isSwitchingCamera = false;
      });
    }
  }

  // 循环切换闪光灯模式
  void _cycleFlashMode() {
    FlashMode nextMode;
    switch (_flashMode) {
      case FlashMode.auto:
        nextMode = FlashMode.off;
        break;
      case FlashMode.off:
        nextMode = FlashMode.always;
        break;
      case FlashMode.always:
        nextMode = FlashMode.auto;
        break;
      case FlashMode.torch:
        nextMode = FlashMode.auto;
        break;
    }

    _safeExecuteCameraOperation(
      () => _controller!.setFlashMode(nextMode),
      operationName: '切换闪光灯模式',
    ).then((_) {
      if (mounted) {
        setState(() {
          _flashMode = nextMode;
        });
      }
    });
  }

  img.Image _addWatermark(img.Image image) {
    // 如果选择了PNG水印，应用PNG水印
    final selectedWatermark = _watermarkManager.selectedWatermark;
    if (selectedWatermark != null) {
      // 获取实际的相机预览尺寸和图像尺寸
      final screenSize = MediaQuery.of(context).size;

      // 计算相机预览的实际显示区域
      Size actualPreviewSize;
      if (_controller != null && _controller!.value.isInitialized) {
        final aspectRatio = _controller!.value.aspectRatio;
        // 根据相机的宽高比计算实际预览尺寸
        if (aspectRatio > screenSize.width / screenSize.height) {
          // 相机更宽，以宽度为准
          actualPreviewSize = Size(
            screenSize.width,
            screenSize.width / aspectRatio,
          );
        } else {
          // 相机更高，以高度为准
          actualPreviewSize = Size(
            screenSize.height * aspectRatio,
            screenSize.height,
          );
        }
      } else {
        // 如果无法获取相机信息，使用屏幕尺寸
        actualPreviewSize = screenSize;
      }

      print('📐 预览尺寸: ${actualPreviewSize.width}x${actualPreviewSize.height}');
      print('📐 图像尺寸: ${image.width}x${image.height}');
      print(
        '📍 水印偏移: (${selectedWatermark.offsetX}, ${selectedWatermark.offsetY})',
      );

      return _watermarkManager.applyWatermarkToImage(
            image,
            selectedWatermark,
            previewSize: actualPreviewSize,
          ) ??
          image;
    }

    // 如果没有选择PNG水印，直接返回原图（不添加任何水印）
    return image;
  }

  // 检查相机是否准备就绪
  bool get _isCameraReady {
    return _controller != null &&
        _controller!.value.isInitialized &&
        !_controller!.value.hasError &&
        !_isDisposing;
  }

  // 初始化相机能力参数
  Future<void> _initializeCameraCapabilities() async {
    if (!_isCameraReady) return;

    try {
      // 获取缩放范围
      _minZoom = await _controller!.getMinZoomLevel();
      _maxZoom = await _controller!.getMaxZoomLevel();

      // 获取曝光范围
      _minExposure = await _controller!.getMinExposureOffset();
      _maxExposure = await _controller!.getMaxExposureOffset();

      // 设置聚焦模式为自动聚焦，提升照片清晰度
      try {
        await _controller!.setFocusMode(FocusMode.auto);
        print('✅ 设置自动聚焦模式成功');
      } catch (e) {
        print('⚠️ 设置聚焦模式失败: $e');
      }

      // 设置曝光模式为自动曝光
      try {
        await _controller!.setExposureMode(ExposureMode.auto);
        print('✅ 设置自动曝光模式成功');
      } catch (e) {
        print('⚠️ 设置曝光模式失败: $e');
      }

      print(
        '📷 相机能力参数: 缩放 $_minZoom-$_maxZoom, 曝光 $_minExposure-$_maxExposure',
      );
    } catch (e) {
      print('⚠️ 获取相机能力参数失败: $e');
    }
  }

  // 安全执行相机操作
  Future<void> _safeExecuteCameraOperation(
    Future<void> Function() operation, {
    required String operationName,
  }) async {
    if (!_isCameraReady) {
      print('⚠️ 相机未准备好，无法执行操作: $operationName');
      return;
    }

    try {
      await operation();
      print('✅ 相机操作成功: $operationName');
    } catch (e) {
      print('❌ 相机操作失败: $operationName - $e');
    }
  }

  // 构建相机预览组件（保持宽高比，支持点击聚焦）
  Widget _buildCameraPreview() {
    print(
      '🖼️ 构建相机预览 - _isInitialized: $_isInitialized, _isCameraReady: $_isCameraReady',
    );

    if (!_isInitialized || !_isCameraReady) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (_isInitializing) ...[
                const CircularProgressIndicator(color: Colors.white),
                const SizedBox(height: 16),
                const Text(
                  '正在初始化相机...',
                  style: TextStyle(color: Colors.white, fontSize: 16),
                  textAlign: TextAlign.center,
                ),
              ] else ...[
                const Icon(Icons.camera_alt, color: Colors.white54, size: 64),
                const SizedBox(height: 16),
                const Text(
                  '相机未准备就绪',
                  style: TextStyle(color: Colors.white54, fontSize: 16),
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      );
    }

    // 相机已准备就绪，显示预览
    return GestureDetector(
      onTapUp: (details) => _onTapToFocus(details),
      onScaleStart: (details) => _onScaleStart(details),
      onScaleUpdate: (details) => _onScaleUpdate(details),
      child: _buildClippedCameraPreview(),
    );
  }

  // 构建裁切后的相机预览
  Widget _buildClippedCameraPreview() {
    final selectedWatermark = _watermarkManager.selectedWatermark;

    if (selectedWatermark != null && selectedWatermark.uiImage != null) {
      // 有水印时，根据水印尺寸裁切相机预览
      final watermarkSize = selectedWatermark.originalSize!;
      final watermarkAspectRatio = watermarkSize.width / watermarkSize.height;

      return AspectRatio(
        aspectRatio: watermarkAspectRatio,
        child: ClipRect(
          child: Stack(
            children: [
              // 相机预览 - 使用FittedBox确保填满裁切区域
              FittedBox(
                fit: BoxFit.cover,
                child: SizedBox(
                  width: watermarkSize.width,
                  height: watermarkSize.height,
                  child: CameraPreview(_controller!),
                ),
              ),

              // 水印叠加层
              if (_watermarkManager.selectedWatermark != null)
                _buildWatermarkOverlay(),

              // 聚焦环
              if (_showFocusRing) _buildFocusRing(),
            ],
          ),
        ),
      );
    } else {
      // 没有水印时，使用正确的预览布局
      // 宽度填满App宽度，高度等比缩放，上下居中
      return LayoutBuilder(
        builder: (context, constraints) {
          final screenWidth = constraints.maxWidth;
          final screenHeight = constraints.maxHeight;

          // 获取相机的宽高比
          final cameraAspectRatio = _controller!.value.aspectRatio;

          // 计算预览高度（宽度固定为屏幕宽度）
          final previewHeight = screenWidth / cameraAspectRatio;

          return Container(
            width: screenWidth,
            height: screenHeight,
            color: Colors.black,
            child: Center(
              child: SizedBox(
                width: screenWidth,
                height: previewHeight,
                child: Stack(
                  children: [
                    // 相机预览 - 宽度填满，高度等比缩放
                    ClipRect(
                      child: SizedBox(
                        width: screenWidth,
                        height: previewHeight,
                        child: FittedBox(
                          fit: BoxFit.cover,
                          child: SizedBox(
                            width: _controller!.value.previewSize!.height,
                            height: _controller!.value.previewSize!.width,
                            child: CameraPreview(_controller!),
                          ),
                        ),
                      ),
                    ),

                    // 聚焦环
                    if (_showFocusRing) _buildFocusRing(),
                  ],
                ),
              ),
            ),
          );
        },
      );
    }
  }

  // 构建水印叠加层
  Widget _buildWatermarkOverlay() {
    final selectedWatermark = _watermarkManager.selectedWatermark;
    if (selectedWatermark == null || selectedWatermark.uiImage == null) {
      return const SizedBox.shrink();
    }

    // 获取屏幕尺寸
    final screenSize = MediaQuery.of(context).size;
    final watermarkRect = _watermarkManager.calculateWatermarkRect(
      screenSize,
      selectedWatermark,
    );

    // 计算位置偏移
    final offsetX =
        watermarkRect.left +
        (selectedWatermark.offsetX * watermarkRect.width / 2);
    final offsetY =
        watermarkRect.top +
        (selectedWatermark.offsetY * watermarkRect.height / 2);

    return Positioned(
      left: offsetX,
      top: offsetY,
      child: GestureDetector(
        // 只使用scale手势，它包含了pan功能，避免冲突
        onScaleStart: (details) => _onWatermarkScaleStart(details),
        onScaleUpdate: (details) => _onWatermarkScaleUpdate(details),
        child: Transform.scale(
          scale: selectedWatermark.scale,
          child: Transform.rotate(
            angle: selectedWatermark.rotation,
            child: Transform(
              alignment: Alignment.center,
              transform: Matrix4.identity()
                ..scale(
                  selectedWatermark.flipHorizontal ? -1.0 : 1.0,
                  selectedWatermark.flipVertical ? -1.0 : 1.0,
                ),
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.5),
                    width: 1,
                  ),
                ),
                child: SizedBox(
                  width: watermarkRect.width,
                  height: watermarkRect.height,
                  child: CustomPaint(
                    painter: HighQualityWatermarkPainter(
                      image: selectedWatermark.uiImage!,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 构建聚焦环
  Widget _buildFocusRing() {
    return Positioned(
      left: _focusPosition.dx - 40,
      top: _focusPosition.dy - 40,
      child: AnimatedBuilder(
        animation: _focusAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _focusAnimation.value,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2),
              ),
              child: Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.yellow, width: 1),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // 点击聚焦
  void _onTapToFocus(TapUpDetails details) {
    if (!_isCameraReady) return;

    final offset = details.localPosition;

    // 显示聚焦环
    setState(() {
      _focusPosition = offset;
      _showFocusRing = true;
    });

    // 启动聚焦环动画
    _focusAnimationController.reset();
    _focusAnimationController.forward();

    // 2秒后隐藏聚焦环
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _showFocusRing = false;
        });
      }
    });

    // 计算正确的聚焦坐标
    double x, y;
    final selectedWatermark = _watermarkManager.selectedWatermark;

    if (selectedWatermark != null && selectedWatermark.uiImage != null) {
      // 有水印时，相机预览被裁切了，需要重新计算坐标
      final watermarkSize = selectedWatermark.originalSize!;
      final watermarkAspectRatio = watermarkSize.width / watermarkSize.height;
      final screenSize = MediaQuery.of(context).size;

      // 计算裁切后的预览区域尺寸
      double previewWidth, previewHeight;
      final screenAspectRatio = screenSize.width / screenSize.height;

      if (watermarkAspectRatio > screenAspectRatio) {
        // 水印更宽，以宽度为准
        previewWidth = screenSize.width;
        previewHeight = screenSize.width / watermarkAspectRatio;
      } else {
        // 水印更高，以高度为准
        previewHeight = screenSize.height;
        previewWidth = screenSize.height * watermarkAspectRatio;
      }

      // 将点击坐标转换为相机坐标(0-1范围)
      x = offset.dx / previewWidth;
      y = offset.dy / previewHeight;
    } else {
      // 没有水印时，需要考虑新的预览布局
      final screenSize = MediaQuery.of(context).size;
      final cameraAspectRatio = _controller!.value.aspectRatio;

      // 计算实际预览区域的高度（宽度固定为屏幕宽度）
      final previewHeight = screenSize.width / cameraAspectRatio;

      // 计算预览区域在屏幕中的垂直偏移（居中显示）
      final verticalOffset = (screenSize.height - previewHeight) / 2;

      // 调整点击坐标，考虑预览区域的实际位置
      final adjustedY = offset.dy - verticalOffset;

      // 转换为相机坐标(0-1范围)
      x = offset.dx / screenSize.width;
      y = adjustedY / previewHeight;
    }

    // 确保坐标在有效范围内
    x = x.clamp(0.0, 1.0);
    y = y.clamp(0.0, 1.0);

    // 智能聚焦系统
    _attemptSmartFocus(Offset(x, y), offset);
  }

  // 智能聚焦尝试
  Future<void> _attemptSmartFocus(
    Offset focusPoint,
    Offset displayPoint,
  ) async {
    try {
      print(
        '📍 尝试聚焦点: (${focusPoint.dx.toStringAsFixed(2)}, ${focusPoint.dy.toStringAsFixed(2)})',
      );

      // 第一次尝试：标准聚焦
      bool success = await _performFocus(focusPoint);

      if (!success) {
        print('⚠️ 标准聚焦失败，尝试备用策略');
        await _fallbackFocusStrategy(focusPoint);
      }
    } catch (e) {
      print('❌ 智能聚焦系统错误: $e');
    }
  }

  // 执行聚焦操作
  Future<bool> _performFocus(Offset focusPoint) async {
    try {
      await _controller!.setFocusPoint(focusPoint);
      await _controller!.setExposurePoint(focusPoint);

      // 等待聚焦完成
      await Future.delayed(const Duration(milliseconds: 500));

      print('✅ 聚焦完成');
      return true;
    } catch (e) {
      print('❌ 聚焦失败: $e');
      return false;
    }
  }

  // 备用聚焦策略
  Future<void> _fallbackFocusStrategy(Offset originalPoint) async {
    try {
      print('🔄 执行备用聚焦策略');

      // 策略1: 调整缩放辅助聚焦
      if (_currentZoom < _maxZoom) {
        double newZoom = (_currentZoom * 1.2).clamp(_minZoom, _maxZoom);
        print(
          '📏 调整缩放辅助聚焦: ${_currentZoom.toStringAsFixed(1)}x -> ${newZoom.toStringAsFixed(1)}x',
        );

        await _controller!.setZoomLevel(newZoom);
        setState(() {
          _currentZoom = newZoom;
        });

        // 等待缩放完成后重新聚焦
        await Future.delayed(const Duration(milliseconds: 300));
        await _performFocus(originalPoint);
      }

      // 策略2: 中心点聚焦
      print('🎯 尝试中心点聚焦');
      await _performFocus(const Offset(0.5, 0.5));

      print('✅ 备用聚焦策略完成');
    } catch (e) {
      print('❌ 备用聚焦策略失败: $e');
    }
  }

  // 水印缩放开始
  void _onWatermarkScaleStart(ScaleStartDetails details) {
    final selectedWatermark = _watermarkManager.selectedWatermark;
    if (selectedWatermark == null) return;

    _watermarkInitialScale = selectedWatermark.scale;
    _watermarkInitialRotation = selectedWatermark.rotation;
  }

  // 水印缩放和拖拽更新（统一处理，避免手势冲突）
  void _onWatermarkScaleUpdate(ScaleUpdateDetails details) {
    final selectedWatermark = _watermarkManager.selectedWatermark;
    if (selectedWatermark == null) return;

    setState(() {
      // 处理拖拽（当scale为1.0时，主要是拖拽操作）
      if (details.scale == 1.0 && details.focalPointDelta != Offset.zero) {
        // 获取预览区域尺寸
        final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
        if (renderBox != null) {
          final size = renderBox.size;

          // 计算新的偏移量（相对于预览区域）
          final deltaX = details.focalPointDelta.dx / size.width;
          final deltaY = details.focalPointDelta.dy / size.height;

          selectedWatermark.offsetX = (selectedWatermark.offsetX + deltaX)
              .clamp(-1.0, 1.0);
          selectedWatermark.offsetY = (selectedWatermark.offsetY + deltaY)
              .clamp(-1.0, 1.0);
        }
      }

      // 处理缩放（当scale不为1.0时）
      if (details.scale != 1.0) {
        selectedWatermark.scale = (_watermarkInitialScale * details.scale)
            .clamp(0.1, 3.0);
      }

      // 处理旋转（如果有旋转手势）
      if (details.rotation != 0) {
        selectedWatermark.rotation =
            _watermarkInitialRotation + details.rotation;
      }
    });
  }

  // 缩放开始
  void _onScaleStart(ScaleStartDetails details) {
    _initialScale = _currentZoom;
  }

  // 缩放更新
  void _onScaleUpdate(ScaleUpdateDetails details) {
    if (!_isCameraReady) return;

    final newZoom = (_initialScale * details.scale).clamp(_minZoom, _maxZoom);

    if ((newZoom - _currentZoom).abs() > 0.1) {
      _safeExecuteCameraOperation(
        () => _controller!.setZoomLevel(newZoom),
        operationName: '设置缩放级别',
      ).then((_) {
        if (mounted) {
          setState(() {
            _currentZoom = newZoom;
          });
        }
      });
    }
  }

  // 应用生命周期变化处理
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (!_hasInitializedOnce) return;

    switch (state) {
      case AppLifecycleState.inactive:
        print('📱 应用即将进入后台');
        break;
      case AppLifecycleState.paused:
        print('📱 应用进入后台');
        _isAppInBackground = true;
        break;
      case AppLifecycleState.resumed:
        print('📱 应用回到前台');
        if (_isAppInBackground) {
          _isAppInBackground = false;
          // 延迟检查相机状态，确保系统资源已释放
          // 增加延迟时间，确保其他相机应用完全释放资源
          Future.delayed(const Duration(milliseconds: 1000), () {
            _forceReinitializeCamera();
          });
        } else {
          // 即使不是从后台返回，也要检查相机状态
          // 这处理从相册返回等情况
          Future.delayed(const Duration(milliseconds: 500), () {
            _checkAndRestoreCameraFromGallery();
          });
        }
        break;
      default:
        break;
    }
  }

  // 检查并恢复相机状态（从相册返回时）
  Future<void> _checkAndRestoreCameraFromGallery() async {
    if (!mounted || _isDisposing || _isInitializing) {
      return;
    }

    print('📷 检查相机状态（从相册返回）...');

    try {
      if (_controller != null && _controller!.value.isInitialized) {
        // 检查预览是否被暂停
        if (_controller!.value.isPreviewPaused) {
          print('📷 检测到预览暂停，恢复预览...');
          await _controller!.resumePreview();
          await Future.delayed(const Duration(milliseconds: 200));
        }

        // 强制刷新预览以解决定格问题
        print('📷 强制刷新预览状态...');
        await _ensureCameraPreview();

        // 触发UI重建
        if (mounted) {
          setState(() {});
        }

        print('✅ 相机预览已恢复');
      } else {
        print('❌ 相机控制器状态异常，重新初始化...');
        await _reinitializeCamera();
      }
    } catch (e) {
      print('❌ 恢复相机预览失败: $e');
      // 如果恢复失败，尝试重新初始化
      await _reinitializeCamera();
    }
  }

  // 检查并恢复相机状态
  Future<void> _checkAndRestoreCamera() async {
    if (!mounted || _isDisposing || _isInitializing) {
      return;
    }

    print('� 检查相机状态...');

    // 检查相机是否真正可用
    bool needsReinitialization = false;

    if (_controller == null) {
      print('❌ 相机控制器为空，需要重新初始化');
      needsReinitialization = true;
    } else if (!_controller!.value.isInitialized) {
      print('❌ 相机控制器未初始化，需要重新初始化');
      needsReinitialization = true;
    } else if (_controller!.value.hasError) {
      print('❌ 相机控制器有错误，需要重新初始化');
      needsReinitialization = true;
    } else {
      // 尝试获取相机状态来验证是否真正可用
      try {
        await _controller!.getMinZoomLevel();
        print('✅ 相机状态正常');
      } catch (e) {
        print('❌ 相机状态检查失败: $e，需要重新初始化');
        needsReinitialization = true;
      }
    }

    if (needsReinitialization) {
      print('🔄 从后台恢复时重新初始化相机');
      await _reinitializeCamera();
    }
  }

  // 强制重新初始化相机（用于从其他相机应用返回时）
  Future<void> _forceReinitializeCamera() async {
    if (!mounted || _isDisposing || _isInitializing) {
      return;
    }

    print('🔄 强制重新初始化相机（从其他相机应用返回）');

    try {
      // 先完全释放当前相机资源
      if (_controller != null) {
        await _controller!.dispose();
        _controller = null;
      }

      // 重置状态
      if (mounted) {
        setState(() {
          _isInitialized = false;
        });
      }

      // 等待更长时间确保资源完全释放
      await Future.delayed(const Duration(milliseconds: 800));

      // 重新初始化相机
      await _initializeCamera();
    } catch (e) {
      print('❌ 强制重新初始化相机失败: $e');
      // 如果失败，尝试标准的重新初始化
      await _reinitializeCamera();
    }
  }

  @override
  void dispose() async {
    print('🗑️ 开始清理资源...');
    _isDisposing = true;

    // 移除生命周期观察者
    WidgetsBinding.instance.removeObserver(this);

    // 释放聚焦动画控制器
    try {
      _focusAnimationController.dispose();
    } catch (e) {
      print('⚠️ 释放聚焦动画控制器时出错: $e');
    }

    // 安全释放相机控制器
    if (_controller != null) {
      try {
        print('🗑️ 正在释放相机控制器...');
        await _controller!.dispose();
        print('✅ 相机控制器已释放');
      } catch (e) {
        print('⚠️ 释放相机控制器时出错: $e');
      } finally {
        _controller = null;
      }
    }

    super.dispose();
    print('✅ 资源清理完成');
  }

  @override
  Widget build(BuildContext context) {
    print('🖼️ 构建界面 - 屏幕尺寸: ${MediaQuery.of(context).size}');

    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // 相机预览区域 - 居中显示
            Center(child: _buildCameraPreview()),

            // 顶部状态栏
            Positioned(top: 0, left: 0, right: 0, child: _buildTopStatusBar()),

            // 底部控制栏
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: _buildBottomControls(),
            ),

            // 右侧菜单按钮
            Positioned(top: 60, right: 16, child: _buildMenuButton()),

            // 左侧镜头切换按钮
            if (cameras.length > 1)
              Positioned(top: 60, left: 16, child: _buildCameraSwitchButton()),
          ],
        ),
      ),
    );
  }

  // 构建顶部状态栏
  Widget _buildTopStatusBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.black.withValues(alpha: 0.7), Colors.transparent],
        ),
      ),
      child: Row(
        children: [
          // 应用标题
          const Text(
            '透卡相机',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),

          // 缩放倍率显示
          if (_isCameraReady && _currentZoom > 1.0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${_currentZoom.toStringAsFixed(1)}x',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

          const SizedBox(width: 8),

          // 闪光灯状态指示
          if (_isCameraReady) _buildFlashIndicator(),
        ],
      ),
    );
  }

  // 构建闪光灯指示器
  Widget _buildFlashIndicator() {
    IconData flashIcon;
    switch (_flashMode) {
      case FlashMode.auto:
        flashIcon = Icons.flash_auto;
        break;
      case FlashMode.always:
        flashIcon = Icons.flash_on;
        break;
      case FlashMode.off:
        flashIcon = Icons.flash_off;
        break;
      case FlashMode.torch:
        flashIcon = Icons.flashlight_on;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Icon(flashIcon, color: Colors.white, size: 16),
    );
  }

  // 构建底部控制栏
  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
          colors: [Colors.black.withValues(alpha: 0.8), Colors.transparent],
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 相册按钮
          _buildControlButton(
            icon: Icons.photo_library,
            onPressed: _openGallery,
            tooltip: '相册',
          ),

          // 拍照按钮
          _buildCaptureButton(),

          // 水印菜单按钮
          _buildControlButton(
            icon: Icons.layers,
            onPressed: _openWatermarkMenu,
            tooltip: '水印',
          ),
        ],
      ),
    );
  }

  // 构建控制按钮
  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.5),
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
        ),
        child: IconButton(
          icon: Icon(icon, color: Colors.white),
          onPressed: onPressed,
        ),
      ),
    );
  }

  // 构建拍照按钮
  Widget _buildCaptureButton() {
    return GestureDetector(
      onTap: _isCameraReady && !_isTakingPicture ? _takePicture : null,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: _isTakingPicture
              ? Colors.red.withValues(alpha: 0.7)
              : Colors.white.withValues(alpha: 0.9),
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 4),
        ),
        child: _isTakingPicture
            ? const Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : const Icon(Icons.camera_alt, color: Colors.black, size: 32),
      ),
    );
  }

  // 构建菜单按钮
  Widget _buildMenuButton() {
    return _buildControlButton(
      icon: Icons.more_vert,
      onPressed: _toggleTopMenu,
      tooltip: '菜单',
    );
  }

  // 构建相机切换按钮
  Widget _buildCameraSwitchButton() {
    return _buildControlButton(
      icon: Icons.flip_camera_ios,
      onPressed: _isSwitchingCamera ? null : _switchCamera,
      tooltip: '切换摄像头',
    );
  }

  // 打开相册
  void _openGallery() {
    print('📷 打开相册');
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const GalleryPage()),
    );
  }

  // 打开水印菜单
  void _openWatermarkMenu() {
    setState(() {
      _isLoadingMenu = true;
    });

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PngMenuPage(cameraController: _controller),
      ),
    ).then((_) {
      setState(() {
        _isLoadingMenu = false;
      });
    });
  }

  // 切换顶部菜单
  void _toggleTopMenu() {
    setState(() {
      _showTopMenu = !_showTopMenu;
    });
  }
}

// 高质量水印绘制器
class HighQualityWatermarkPainter extends CustomPainter {
  final ui.Image image;

  HighQualityWatermarkPainter({required this.image});

  @override
  void paint(Canvas canvas, Size size) {
    // 保存画布状态
    canvas.save();

    // 使用最高质量的图像绘制设置
    final paint = Paint()
      ..filterQuality = FilterQuality
          .high // 最高质量滤镜
      ..isAntiAlias =
          true // 启用抗锯齿
      ..color = const Color(0xFFFFFFFF); // 确保颜色正确

    // 获取设备像素比以优化渲染质量
    final pixelRatio =
        ui.PlatformDispatcher.instance.views.first.devicePixelRatio;

    // 使用更精确的尺寸计算，避免模糊
    final targetWidth = size.width * pixelRatio;
    final targetHeight = size.height * pixelRatio;

    // 计算最佳的绘制区域，保持原图宽高比
    final imageAspectRatio = image.width / image.height;
    final canvasAspectRatio = targetWidth / targetHeight;

    double drawWidth, drawHeight;
    double offsetX = 0, offsetY = 0;

    // 优化缩放策略：确保水印完整显示且质量最佳
    if (imageAspectRatio > canvasAspectRatio) {
      // 图像更宽，以宽度为准
      drawWidth = targetWidth;
      drawHeight = targetWidth / imageAspectRatio;
      offsetY = (targetHeight - drawHeight) / 2;
    } else {
      // 图像更高，以高度为准
      drawHeight = targetHeight;
      drawWidth = targetHeight * imageAspectRatio;
      offsetX = (targetWidth - drawWidth) / 2;
    }

    // 转换回逻辑像素
    drawWidth /= pixelRatio;
    drawHeight /= pixelRatio;
    offsetX /= pixelRatio;
    offsetY /= pixelRatio;

    // 使用高质量绘制，直接绘制原图避免多次缩放
    canvas.drawImageRect(
      image,
      Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()),
      Rect.fromLTWH(offsetX, offsetY, drawWidth, drawHeight),
      paint,
    );

    // 恢复画布状态
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! HighQualityWatermarkPainter ||
        oldDelegate.image != image;
  }
}
