import 'package:flutter/material.dart';

/// 右上角折叠菜单面板
class TopMenuPanel extends StatelessWidget {
  final bool isVisible;
  final VoidCallback onCameraControlsTap;
  final VoidCallback onSystemCameraTap;
  final VoidCallback onWatermarkMenuTap;
  final VoidCallback onClose;

  const TopMenuPanel({
    super.key,
    required this.isVisible,
    required this.onCameraControlsTap,
    required this.onSystemCameraTap,
    required this.onWatermarkMenuTap,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    return Positioned(
      top: MediaQuery.of(context).padding.top + 60,
      right: 20,
      child: AnimatedOpacity(
        opacity: isVisible ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 200),
        child: Container(
          width: 200,
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.9),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withOpacity(0.3)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.5),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Colors.white.withOpacity(0.2)),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.menu, color: Colors.white, size: 18),
                    const SizedBox(width: 8),
                    const Text(
                      '功能菜单',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: onClose,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 菜单项列表
              _buildMenuItem(
                icon: Icons.tune,
                title: '相机控制',
                subtitle: '缩放、闪光灯、曝光',
                onTap: onCameraControlsTap,
              ),

              _buildMenuItem(
                icon: Icons.camera_alt_outlined,
                title: '系统相机',
                subtitle: '调用系统相机拍照',
                onTap: onSystemCameraTap,
              ),

              _buildMenuItem(
                icon: Icons.layers,
                title: '水印设置',
                subtitle: '选择和调整水印',
                onTap: onWatermarkMenuTap,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(18),
              ),
              child: Icon(icon, color: Colors.white, size: 18),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.white.withOpacity(0.5),
              size: 12,
            ),
          ],
        ),
      ),
    );
  }
}

/// 右上角菜单按钮
class TopMenuButton extends StatelessWidget {
  final bool isMenuOpen;
  final VoidCallback onTap;

  const TopMenuButton({
    super.key,
    required this.isMenuOpen,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 20,
      right: 20,
      child: GestureDetector(
        onTap: onTap,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: isMenuOpen
                ? Colors.blue.withOpacity(0.8)
                : Colors.black.withOpacity(0.7),
            shape: BoxShape.circle,
            border: Border.all(
              color: isMenuOpen ? Colors.blue : Colors.white,
              width: 2,
            ),
          ),
          child: AnimatedRotation(
            turns: isMenuOpen ? 0.125 : 0.0, // 45度旋转
            duration: const Duration(milliseconds: 200),
            child: const Icon(Icons.menu, color: Colors.white, size: 24),
          ),
        ),
      ),
    );
  }
}

/// 底部操作按钮组
class BottomActionButtons extends StatelessWidget {
  final VoidCallback onGalleryTap;
  final VoidCallback onCaptureTap;
  final VoidCallback onSwitchCameraTap;
  final bool isTakingPicture;
  final bool isSwitchingCamera;
  final bool hasMultipleCameras;

  const BottomActionButtons({
    super.key,
    required this.onGalleryTap,
    required this.onCaptureTap,
    required this.onSwitchCameraTap,
    required this.isTakingPicture,
    required this.isSwitchingCamera,
    required this.hasMultipleCameras,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 相册按钮
        _buildActionButton(
          icon: Icons.photo_library,
          onTap: onGalleryTap,
          size: 60,
        ),

        // 拍照按钮（中间，保持原有样式）
        GestureDetector(
          onTap: isTakingPicture ? null : onCaptureTap,
          child: Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
              border: Border.all(color: Colors.white, width: 3),
            ),
            child: isTakingPicture
                ? const Padding(
                    padding: EdgeInsets.all(20),
                    child: CircularProgressIndicator(strokeWidth: 3),
                  )
                : const Icon(Icons.camera_alt, size: 35, color: Colors.black),
          ),
        ),

        // 摄像头切换按钮
        _buildActionButton(
          icon: Icons.flip_camera_ios,
          onTap: hasMultipleCameras && !isSwitchingCamera
              ? onSwitchCameraTap
              : null,
          size: 60,
          isLoading: isSwitchingCamera,
          isEnabled: hasMultipleCameras,
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback? onTap,
    required double size,
    bool isLoading = false,
    bool isEnabled = true,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          shape: BoxShape.circle,
          border: Border.all(
            color: isEnabled ? Colors.white : Colors.grey,
            width: 2,
          ),
        ),
        child: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Icon(
                icon,
                color: isEnabled ? Colors.white : Colors.grey,
                size: size * 0.45,
              ),
      ),
    );
  }
}
