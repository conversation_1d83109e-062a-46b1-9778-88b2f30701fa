import 'package:flutter/material.dart';
import 'package:camera/camera.dart';

/// 相机控制面板组件
class CameraControlsPanel extends StatelessWidget {
  final CameraController? controller;
  final double currentZoom;
  final double minZoom;
  final double maxZoom;
  final FlashMode flashMode;
  final double exposureOffset;
  final double minExposure;
  final double maxExposure;
  final Function(double) onZoomChanged;
  final Function(FlashMode) onFlashModeChanged;
  final Function(double) onExposureChanged;

  const CameraControlsPanel({
    super.key,
    required this.controller,
    required this.currentZoom,
    required this.minZoom,
    required this.maxZoom,
    required this.flashMode,
    required this.exposureOffset,
    required this.minExposure,
    required this.maxExposure,
    required this.onZoomChanged,
    required this.onFlashModeChanged,
    required this.onExposureChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 220,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题
          const Row(
            children: [
              Icon(Icons.tune, color: Colors.white, size: 20),
              SizedBox(width: 8),
              Text(
                '相机控制',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // 缩放控制
          _buildZoomControl(),
          const SizedBox(height: 16),

          // 闪光灯控制
          _buildFlashControl(),
          const SizedBox(height: 16),

          // 曝光控制
          _buildExposureControl(),
        ],
      ),
    );
  }

  // 构建缩放控制
  Widget _buildZoomControl() {
    return Column(
      children: [
        Row(
          children: [
            const Icon(Icons.zoom_in, color: Colors.white, size: 18),
            const SizedBox(width: 8),
            Text(
              '缩放 ${currentZoom.toStringAsFixed(1)}x',
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: currentZoom,
          min: minZoom,
          max: maxZoom,
          divisions: 20,
          activeColor: Colors.blue,
          inactiveColor: Colors.grey,
          onChanged: onZoomChanged,
        ),
      ],
    );
  }

  // 构建闪光灯控制
  Widget _buildFlashControl() {
    return Column(
      children: [
        const Row(
          children: [
            Icon(Icons.flash_on, color: Colors.white, size: 18),
            SizedBox(width: 8),
            Text('闪光灯', style: TextStyle(color: Colors.white, fontSize: 14)),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildFlashButton(FlashMode.auto, Icons.flash_auto, '自动'),
            _buildFlashButton(FlashMode.off, Icons.flash_off, '关闭'),
            _buildFlashButton(FlashMode.always, Icons.flash_on, '开启'),
          ],
        ),
      ],
    );
  }

  // 构建闪光灯按钮
  Widget _buildFlashButton(FlashMode mode, IconData icon, String label) {
    final isSelected = flashMode == mode;
    return GestureDetector(
      onTap: () => onFlashModeChanged(mode),
      child: Container(
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue.withOpacity(0.3) : Colors.transparent,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey,
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(icon, color: Colors.white, size: 14),
            const SizedBox(height: 2),
            Text(
              label,
              style: const TextStyle(color: Colors.white, fontSize: 10),
            ),
          ],
        ),
      ),
    );
  }

  // 构建曝光控制
  Widget _buildExposureControl() {
    return Column(
      children: [
        Row(
          children: [
            const Icon(Icons.exposure, color: Colors.white, size: 18),
            const SizedBox(width: 8),
            Text(
              '曝光 ${exposureOffset.toStringAsFixed(1)}',
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: exposureOffset,
          min: minExposure,
          max: maxExposure,
          divisions: 20,
          activeColor: Colors.orange,
          inactiveColor: Colors.grey,
          onChanged: onExposureChanged,
        ),
      ],
    );
  }
}

/// 底部小图标控制区域
class BottomCameraControls extends StatelessWidget {
  final double currentZoom;
  final double minZoom;
  final double maxZoom;
  final FlashMode flashMode;
  final double exposureOffset;
  final Function(double) onZoomChanged;
  final Function() onFlashTapped;
  final Function(double) onExposureChanged;

  const BottomCameraControls({
    super.key,
    required this.currentZoom,
    required this.minZoom,
    required this.maxZoom,
    required this.flashMode,
    required this.exposureOffset,
    required this.onZoomChanged,
    required this.onFlashTapped,
    required this.onExposureChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.6),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 缩放控制
          _buildQuickControl(
            icon: Icons.zoom_in,
            label: '${currentZoom.toStringAsFixed(1)}x',
            onTap: () => _showZoomSlider(context),
          ),
          
          const SizedBox(width: 20),
          
          // 闪光灯控制
          _buildQuickControl(
            icon: _getFlashIcon(),
            label: _getFlashLabel(),
            onTap: onFlashTapped,
          ),
          
          const SizedBox(width: 20),
          
          // 曝光控制
          _buildQuickControl(
            icon: Icons.exposure,
            label: exposureOffset.toStringAsFixed(1),
            onTap: () => _showExposureSlider(context),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickControl({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.white, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getFlashIcon() {
    switch (flashMode) {
      case FlashMode.auto:
        return Icons.flash_auto;
      case FlashMode.always:
        return Icons.flash_on;
      case FlashMode.off:
        return Icons.flash_off;
      case FlashMode.torch:
        return Icons.flashlight_on;
    }
  }

  String _getFlashLabel() {
    switch (flashMode) {
      case FlashMode.auto:
        return '自动';
      case FlashMode.always:
        return '开启';
      case FlashMode.off:
        return '关闭';
      case FlashMode.torch:
        return '常亮';
    }
  }

  void _showZoomSlider(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withOpacity(0.8),
        title: const Text('缩放调节', style: TextStyle(color: Colors.white)),
        content: StatefulBuilder(
          builder: (context, setState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '${currentZoom.toStringAsFixed(1)}x',
                style: const TextStyle(color: Colors.white, fontSize: 18),
              ),
              Slider(
                value: currentZoom,
                min: minZoom,
                max: maxZoom,
                divisions: 20,
                activeColor: Colors.blue,
                onChanged: (value) {
                  setState(() {});
                  onZoomChanged(value);
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定', style: TextStyle(color: Colors.blue)),
          ),
        ],
      ),
    );
  }

  void _showExposureSlider(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withOpacity(0.8),
        title: const Text('曝光调节', style: TextStyle(color: Colors.white)),
        content: StatefulBuilder(
          builder: (context, setState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                exposureOffset.toStringAsFixed(1),
                style: const TextStyle(color: Colors.white, fontSize: 18),
              ),
              Slider(
                value: exposureOffset,
                min: -2.0,
                max: 2.0,
                divisions: 20,
                activeColor: Colors.orange,
                onChanged: (value) {
                  setState(() {});
                  onExposureChanged(value);
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定', style: TextStyle(color: Colors.blue)),
          ),
        ],
      ),
    );
  }
}
